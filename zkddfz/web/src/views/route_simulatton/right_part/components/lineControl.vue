<template>
  <div
    class="futuristic-card-symmetric relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 border border-cyan-500/30 shadow-lg shadow-cyan-500/10 hover:shadow-cyan-500/20 transition-all duration-300 w-full"
  >
    <!-- Card Glow Effect -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md"
    ></div>

    <!-- Corner Decorations -->
    <div class="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-cyan-400/60"></div>
    <div class="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-cyan-400/60"></div>
    <div class="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-cyan-400/60"></div>
    <div class="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-cyan-400/60"></div>

    <!-- Panel Header -->
    <div
      class="relative z-10 flex justify-between items-center p-4 border-b border-cyan-500/20 cursor-pointer hover:bg-slate-700/30 transition-all duration-200"
      @click="togglePanel"
    >
      <div>
        <h3 class="text-lg font-semibold text-cyan-400 mb-1">路线模拟控制</h3>
        <div class="w-16 h-0.5 bg-gradient-to-r from-cyan-400 to-transparent rounded-full"></div>
      </div>
      <div
        class="flex items-center justify-center w-8 h-8 bg-slate-700/60 border border-cyan-500/30 rounded transition-all duration-200 hover:border-cyan-400/50"
      >
        <svg
          :class="['w-5 h-5 text-cyan-400 transition-transform duration-300', { 'rotate-180': isExpanded }]"
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M7 10l5 5 5-5z" />
        </svg>
      </div>
    </div>

    <!-- Panel Content -->
    <div v-show="isExpanded" class="relative z-10 p-6 space-y-8">
      <!-- Data Source Selection -->
      <div class="space-y-4">
        <div class="flex items-center gap-2 mb-4">
          <div class="w-2 h-2 bg-cyan-400 rounded-full shadow-sm shadow-cyan-400/50"></div>
          <label class="text-sm font-semibold text-white">数据源选择</label>
        </div>
        <div class="grid grid-cols-1 gap-3">
          <button
            :class="[
              'control-btn-symmetric h-12 relative overflow-hidden group transition-all duration-300 font-medium text-sm',
              dataSource === 'json'
                ? 'bg-gradient-to-r from-cyan-600/70 to-cyan-500/50 border border-cyan-400 text-cyan-200'
                : 'bg-gradient-to-r from-slate-700/60 to-slate-600/40 border border-cyan-500/30 text-slate-300 hover:from-slate-600/70 hover:to-slate-500/50',
            ]"
            @click="setDataSource('json')"
          >
            <span class="relative z-10">JSON数组数据源</span>
            <div
              class="absolute inset-0 bg-gradient-to-r from-cyan-400/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
            ></div>
          </button>
          <button
            :class="[
              'control-btn-symmetric h-12 relative overflow-hidden group transition-all duration-300 font-medium text-sm',
              dataSource === 'websocket'
                ? 'bg-gradient-to-r from-cyan-600/70 to-cyan-500/50 border border-cyan-400 text-cyan-200'
                : 'bg-gradient-to-r from-slate-700/60 to-slate-600/40 border border-cyan-500/30 text-slate-300 hover:from-slate-600/70 hover:to-slate-500/50',
            ]"
            @click="setDataSource('websocket')"
          >
            <span class="relative z-10">WebSocket实时数据源</span>
            <div
              class="absolute inset-0 bg-gradient-to-r from-cyan-400/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
            ></div>
          </button>
        </div>
      </div>

      <!-- JSON Data Input -->
      <div v-if="dataSource === 'json'" class="space-y-6">
        <div class="space-y-4">
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-cyan-400 rounded-full shadow-sm shadow-cyan-400/50"></div>
            <label class="text-sm font-semibold text-white">路线数据配置</label>
          </div>

          <div class="space-y-3">
            <label class="block text-xs font-medium text-slate-400 uppercase tracking-wider">JSON格式路线数据</label>
            <textarea
              v-model="jsonInput"
              class="futuristic-input-symmetric w-full px-4 py-4 bg-slate-700/60 border border-cyan-500/30 text-white text-sm placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50 resize-vertical font-mono"
              placeholder="输入JSON格式的路线数据..."
              rows="8"
            ></textarea>
          </div>

          <!-- Preset Buttons -->
          <div class="space-y-3">
            <label class="block text-xs font-medium text-slate-400 uppercase tracking-wider">预设路线模板</label>
            <div class="grid grid-cols-2 gap-3">
              <button
                @click="loadSampleData"
                class="control-btn-symmetric h-11 relative overflow-hidden group bg-gradient-to-r from-slate-700/60 to-slate-600/40 border border-cyan-500/30 text-slate-300 hover:from-slate-600/70 hover:to-slate-500/50 transition-all duration-300 font-medium text-sm"
              >
                <span class="relative z-10">转弯模拟</span>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                ></div>
              </button>
              <button
                @click="loadCityRouteData"
                class="control-btn-symmetric h-11 relative overflow-hidden group bg-gradient-to-r from-slate-700/60 to-slate-600/40 border border-cyan-500/30 text-slate-300 hover:from-slate-600/70 hover:to-slate-500/50 transition-all duration-300 font-medium text-sm"
              >
                <span class="relative z-10">排布模拟</span>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                ></div>
              </button>
              <button
                @click="loadCircularRouteData"
                class="control-btn-symmetric h-11 relative overflow-hidden group bg-gradient-to-r from-slate-700/60 to-slate-600/40 border border-cyan-500/30 text-slate-300 hover:from-slate-600/70 hover:to-slate-500/50 transition-all duration-300 font-medium text-sm"
              >
                <span class="relative z-10">能耗模拟</span>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                ></div>
              </button>
              <button
                @click="loadForwardBackwardRouteData"
                class="control-btn-symmetric h-11 relative overflow-hidden group bg-gradient-to-r from-slate-700/60 to-slate-600/40 border border-cyan-500/30 text-slate-300 hover:from-slate-600/70 hover:to-slate-500/50 transition-all duration-300 font-medium text-sm"
              >
                <span class="relative z-10">感知模拟</span>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                ></div>
              </button>
            </div>
          </div>

          <button
            @click="loadJsonData"
            class="control-btn-symmetric w-full h-12 relative overflow-hidden group bg-gradient-to-r from-cyan-600/50 to-cyan-500/30 border border-cyan-500 text-cyan-300 hover:from-cyan-600/70 hover:to-cyan-500/50 transition-all duration-300 font-medium text-sm"
          >
            <span class="relative z-10">加载路线数据</span>
            <div
              class="absolute inset-0 bg-gradient-to-r from-cyan-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
            ></div>
          </button>
        </div>
      </div>

      <!-- WebSocket Configuration -->
      <div v-if="dataSource === 'websocket'" class="space-y-6">
        <div class="space-y-4">
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-cyan-400 rounded-full shadow-sm shadow-cyan-400/50"></div>
            <label class="text-sm font-semibold text-white">WebSocket连接配置</label>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-xs font-medium text-slate-400 mb-2 uppercase tracking-wider"
                >WebSocket服务器地址</label
              >
              <input
                v-model="websocketUrl"
                type="text"
                class="futuristic-input-symmetric w-full px-4 py-3 bg-slate-700/60 border border-cyan-500/30 text-white text-sm placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
                placeholder="ws://localhost:8080/route"
              />
            </div>

            <button
              @click="toggleWebSocket"
              :class="[
                'control-btn-symmetric w-full h-12 relative overflow-hidden group transition-all duration-300 font-medium text-sm',
                websocketConnected
                  ? 'bg-gradient-to-r from-red-600/50 to-red-500/30 border border-red-500 text-red-400 hover:from-red-600/70 hover:to-red-500/50'
                  : 'bg-gradient-to-r from-cyan-600/50 to-cyan-500/30 border border-cyan-500 text-cyan-300 hover:from-cyan-600/70 hover:to-cyan-500/50',
              ]"
            >
              <span class="relative z-10">{{ websocketConnected ? '断开WebSocket连接' : '连接WebSocket服务器' }}</span>
              <div
                class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                :class="
                  websocketConnected
                    ? 'bg-gradient-to-r from-red-400/30 to-transparent'
                    : 'bg-gradient-to-r from-cyan-400/30 to-transparent'
                "
              ></div>
            </button>

            <!-- Connection Status -->
            <div
              class="control-btn-symmetric relative overflow-hidden group bg-gradient-to-r from-slate-700/60 to-slate-600/40 border border-cyan-500/30 p-4 transition-all duration-300 hover:from-slate-600/70 hover:to-slate-500/50 hover:border-cyan-400/50"
            >
              <div class="flex items-center space-x-4 relative z-10">
                <div
                  :class="[
                    'w-4 h-4 rounded-full transition-all duration-200',
                    websocketConnected
                      ? 'bg-green-500 shadow-sm shadow-green-500/50 animate-pulse'
                      : 'bg-red-500 shadow-sm shadow-red-500/50',
                  ]"
                ></div>
                <div class="flex-1">
                  <span class="text-sm font-medium text-slate-200 block">连接状态</span>
                  <span :class="['text-xs mt-1 block', websocketConnected ? 'text-green-400' : 'text-red-400']">
                    {{ websocketConnected ? 'WebSocket连接已建立' : 'WebSocket连接未建立' }}
                  </span>
                </div>
              </div>
              <div
                class="absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm pointer-events-none"
              ></div>
            </div>

            <!-- WebSocket 控制按钮 -->
            <div v-if="websocketConnected" class="grid grid-cols-2 gap-3">
              <button
                @click="sendTestMessage"
                class="control-btn-symmetric h-10 relative overflow-hidden group bg-gradient-to-r from-blue-600/50 to-blue-500/30 border border-blue-500 text-blue-300 hover:from-blue-600/70 hover:to-blue-500/50 transition-all duration-300 font-medium text-xs"
              >
                <span class="relative z-10">发送测试消息</span>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-blue-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                ></div>
              </button>
              <button
                @click="requestRouteData"
                class="control-btn-symmetric h-10 relative overflow-hidden group bg-gradient-to-r from-purple-600/50 to-purple-500/30 border border-purple-500 text-purple-300 hover:from-purple-600/70 hover:to-purple-500/50 transition-all duration-300 font-medium text-xs"
              >
                <span class="relative z-10">请求路线数据</span>
                <div
                  class="absolute inset-0 bg-gradient-to-r from-purple-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
                ></div>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Playback Controls -->
      <div class="space-y-6">
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-cyan-400 rounded-full shadow-sm shadow-cyan-400/50"></div>
          <label class="text-sm font-semibold text-white">仿真控制</label>
        </div>

        <div class="grid grid-cols-2 gap-3">
          <button
            @click="startRoute"
            :disabled="!canStart"
            class="control-btn-symmetric h-12 relative overflow-hidden group bg-gradient-to-r from-green-600/50 to-green-500/30 border border-green-500 text-green-300 hover:from-green-600/70 hover:to-green-500/50 transition-all duration-300 font-medium text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="relative z-10">开始仿真</span>
            <div
              class="absolute inset-0 bg-gradient-to-r from-green-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
            ></div>
          </button>
          <button
            @click="pauseRoute"
            :disabled="!simulator.isRunning || simulator.isPaused"
            class="control-btn-symmetric h-12 relative overflow-hidden group bg-gradient-to-r from-yellow-600/50 to-yellow-500/30 border border-yellow-500 text-yellow-300 hover:from-yellow-600/70 hover:to-yellow-500/50 transition-all duration-300 font-medium text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="relative z-10">暂停仿真</span>
            <div
              class="absolute inset-0 bg-gradient-to-r from-yellow-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
            ></div>
          </button>
          <button
            @click="resumeRoute"
            :disabled="!simulator.isPaused"
            class="control-btn-symmetric h-12 relative overflow-hidden group bg-gradient-to-r from-blue-600/50 to-blue-500/30 border border-blue-500 text-blue-300 hover:from-blue-600/70 hover:to-blue-500/50 transition-all duration-300 font-medium text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="relative z-10">继续仿真</span>
            <div
              class="absolute inset-0 bg-gradient-to-r from-blue-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
            ></div>
          </button>
          <button
            @click="stopRoute"
            :disabled="!simulator.isRunning"
            class="control-btn-symmetric h-12 relative overflow-hidden group bg-gradient-to-r from-red-600/50 to-red-500/30 border border-red-500 text-red-400 hover:from-red-600/70 hover:to-red-500/50 transition-all duration-300 font-medium text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="relative z-10">停止仿真</span>
            <div
              class="absolute inset-0 bg-gradient-to-r from-red-400/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"
            ></div>
          </button>
        </div>

        <!-- Progress Display -->
        <div v-if="routeStatus.totalPoints > 0" class="space-y-3">
          <div class="flex justify-between text-sm text-slate-300">
            <span>仿真进度: {{ routeStatus.currentIndex + 1 }} / {{ routeStatus.totalPoints }}</span>
            <span>{{ Math.round(routeStatus.progress * 100) }}%</span>
          </div>
          <div class="w-full h-3 bg-slate-700/60 rounded-full overflow-hidden border border-cyan-500/20">
            <div
              class="h-full bg-gradient-to-r from-cyan-500 to-cyan-400 transition-all duration-300 rounded-full shadow-sm shadow-cyan-500/50"
              :style="{ width: routeStatus.progress * 100 + '%' }"
            ></div>
          </div>
        </div>
      </div>

      <!-- Configuration Options -->
      <div class="space-y-6">
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-cyan-400 rounded-full shadow-sm shadow-cyan-400/50"></div>
          <label class="text-sm font-semibold text-white">仿真配置</label>
        </div>

        <!-- Playback Speed -->
        <div class="space-y-3">
          <label class="block text-xs font-medium text-slate-400 uppercase tracking-wider">仿真速度控制</label>
          <div class="flex items-center gap-4">
            <input
              v-model.number="playbackSpeed"
              type="range"
              min="0.1"
              max="5"
              step="0.1"
              class="flex-1 h-2 bg-slate-700/60 rounded-full appearance-none cursor-pointer slider-thumb"
              @input="updatePlaybackSpeed"
            />
            <span class="text-sm text-cyan-400 font-semibold min-w-16 text-right">{{ playbackSpeed }}x</span>
          </div>
        </div>

        <!-- Interpolation Mode -->
        <div class="space-y-3">
          <label class="block text-xs font-medium text-slate-400 uppercase tracking-wider">插值模式选择</label>
          <select
            v-model="interpolationMode"
            @change="updateInterpolationMode"
            class="futuristic-input-symmetric w-full px-4 py-3 bg-slate-700/60 border border-cyan-500/30 text-white text-sm appearance-none focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
          >
            <option value="linear" class="bg-slate-800 text-white">线性插值</option>
            <option value="smooth" class="bg-slate-800 text-white">平滑插值</option>
          </select>
        </div>

        <!-- Advanced Options -->
        <div class="space-y-4">
          <label class="block text-xs font-medium text-slate-400 uppercase tracking-wider">高级选项</label>

          <!-- Loop Playback -->
          <div
            class="control-btn-symmetric relative overflow-hidden group bg-gradient-to-r from-slate-700/60 to-slate-600/40 border border-cyan-500/30 p-4 transition-all duration-300 hover:from-slate-600/70 hover:to-slate-500/50 hover:border-cyan-400/50 cursor-pointer"
          >
            <div class="flex items-center space-x-4 relative z-10">
              <div class="relative">
                <input
                  type="checkbox"
                  id="loop-playback"
                  v-model="loopPlayback"
                  @change="updateLoopMode"
                  class="sr-only"
                />
                <div
                  class="w-6 h-6 border-2 transition-all duration-200 flex items-center justify-center rounded-sm"
                  :class="
                    loopPlayback
                      ? 'bg-cyan-500/20 border-cyan-400 shadow-sm shadow-cyan-400/50'
                      : 'border-cyan-500/50 bg-slate-800/60 hover:border-cyan-400/70'
                  "
                  @click="
                    loopPlayback = !loopPlayback;
                    updateLoopMode();
                  "
                >
                  <div
                    v-if="loopPlayback"
                    class="w-3 h-3 bg-cyan-400 transition-all duration-200 shadow-sm shadow-cyan-400/50 rounded-sm"
                  ></div>
                </div>
              </div>
              <div class="flex-1">
                <label for="loop-playback" class="text-sm font-medium text-slate-200 cursor-pointer select-none block">
                  循环仿真模式
                </label>
                <p class="text-xs text-slate-400 mt-1">启用后路线仿真完成将自动重新开始</p>
              </div>
            </div>
            <div
              class="absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm pointer-events-none"
            ></div>
          </div>

          <!-- Smooth Rotation -->
          <div
            class="control-btn-symmetric relative overflow-hidden group bg-gradient-to-r from-slate-700/60 to-slate-600/40 border border-cyan-500/30 p-4 transition-all duration-300 hover:from-slate-600/70 hover:to-slate-500/50 hover:border-cyan-400/50 cursor-pointer"
          >
            <div class="flex items-center space-x-4 relative z-10">
              <div class="relative">
                <input
                  type="checkbox"
                  id="smooth-rotation"
                  v-model="smoothRotation"
                  @change="updateSmoothRotation"
                  class="sr-only"
                />
                <div
                  class="w-6 h-6 border-2 transition-all duration-200 flex items-center justify-center rounded-sm"
                  :class="
                    smoothRotation
                      ? 'bg-cyan-500/20 border-cyan-400 shadow-sm shadow-cyan-400/50'
                      : 'border-cyan-500/50 bg-slate-800/60 hover:border-cyan-400/70'
                  "
                  @click="
                    smoothRotation = !smoothRotation;
                    updateSmoothRotation();
                  "
                >
                  <div
                    v-if="smoothRotation"
                    class="w-3 h-3 bg-cyan-400 transition-all duration-200 shadow-sm shadow-cyan-400/50 rounded-sm"
                  ></div>
                </div>
              </div>
              <div class="flex-1">
                <label
                  for="smooth-rotation"
                  class="text-sm font-medium text-slate-200 cursor-pointer select-none block"
                >
                  平滑旋转模式
                </label>
                <p class="text-xs text-slate-400 mt-1">启用平滑的模型旋转动画效果</p>
              </div>
            </div>
            <div
              class="absolute inset-0 bg-gradient-to-r from-cyan-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm pointer-events-none"
            ></div>
          </div>
        </div>
      </div>

      <!-- Status Information -->
      <div class="space-y-4 p-4 bg-slate-700/40 border border-cyan-500/20 rounded">
        <div class="flex items-center gap-2 mb-4">
          <div class="w-2 h-2 bg-green-500 rounded-full shadow-sm shadow-green-500/50"></div>
          <span class="text-sm font-semibold text-cyan-400">系统状态监控</span>
        </div>

        <div class="grid grid-cols-1 gap-4">
          <div class="flex items-center justify-between">
            <span class="text-sm font-semibold text-cyan-400">运行状态:</span>
            <span
              :class="[
                'text-sm font-medium px-3 py-1 rounded text-xs',
                getStatusClass() === 'running'
                  ? 'bg-green-500/20 text-green-400'
                  : getStatusClass() === 'paused'
                  ? 'bg-yellow-500/20 text-yellow-400'
                  : 'bg-red-500/20 text-red-400',
              ]"
            >
              {{ getStatusText() }}
            </span>
          </div>

          <div v-if="currentPosition" class="text-sm text-slate-300">
            <span class="font-semibold text-cyan-400 block mb-1">当前位置坐标:</span>
            <span class="font-mono text-xs bg-slate-800/60 px-2 py-1 rounded">
              X: {{ currentPosition.x.toFixed(2) }}, Y: {{ currentPosition.y.toFixed(2) }}, Z:
              {{ currentPosition.z.toFixed(2) }}
            </span>
          </div>

          <div v-if="currentRotation" class="text-sm text-slate-300">
            <span class="font-semibold text-cyan-400 block mb-1">当前旋转角度:</span>
            <span class="font-mono text-xs bg-slate-800/60 px-2 py-1 rounded">
              X: {{ Math.round(THREE.MathUtils.radToDeg(currentRotation.x)) }}°, Y:
              {{ Math.round(THREE.MathUtils.radToDeg(currentRotation.y)) }}°, Z:
              {{ Math.round(THREE.MathUtils.radToDeg(currentRotation.z)) }}°
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import * as THREE from 'three';
import { computed, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
import { RouteSimulator } from '../../utils/RouteSimulator.js';

const props = defineProps({
  model: {
    type: Object,
    default: null,
  },
  modelComponent: {
    type: Object,
    default: null,
  },
  models: {
    type: Array,
    default: () => [],
  },
  sceneManager: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(['status-change', 'position-update']);

// 数据源
const dataSource = ref('json');
const jsonInput = ref('');
const websocketUrl = ref('ws://localhost:8080/route');
const websocketConnected = ref(false);

// 路线模拟器
const simulator = reactive(
  new RouteSimulator({
    interpolationMode: 'linear',
    speed: 1.0,
    loop: false,
    smoothRotation: true,
  }),
);

// 仿真控制
const playbackSpeed = ref(1.0);
const interpolationMode = ref('linear');
const loopPlayback = ref(false);
const smoothRotation = ref(true);

// 状态
const routeStatus = reactive({
  isRunning: false,
  isPaused: false,
  currentIndex: 0,
  totalPoints: 0,
  progress: 0,
});

const currentPosition = ref(null);
const currentRotation = ref(null);

// 面板展开/关闭状态
const isExpanded = ref(false);

// 计算属性
const canStart = computed(() => {
  const hasModel = !!simulator.model;
  const hasRouteData =
    (dataSource.value === 'json' && simulator.routeData.length > 0) ||
    (dataSource.value === 'websocket' && websocketConnected.value);
  return hasModel && hasRouteData;
});

// 汽车模拟数据（400x400地图范围，平滑行驶路径）
const sampleRouteData2 = [
  {
    timestamp: Date.now(),
    position: { x: 100, y: 0, z: 55.5 },
    rotation: { x: 0, y: 90, z: 0 }, // 面向北方
    duration: 1000,
  },
  {
    timestamp: Date.now() + 1000,
    position: { x: 100, y: 0, z: 98.5 },
    rotation: { x: 0, y: 90, z: 0 }, // 继续向北
    duration: 3000,
  },
  {
    timestamp: Date.now() + 4000,
    position: { x: 85, y: 0, z: 98.5 },
    rotation: { x: 0, y: 90, z: 0 }, // 东北方向
    duration: 2500,
  },
  {
    timestamp: Date.now() + 6500,
    position: { x: 97.8898569830913, y: 0, z: 95.6423875046178 },
    rotation: { x: 0, y: 65, z: 0 }, // 面向东方
    duration: 3000,
  },
  {
    timestamp: Date.now() + 9500,
    position: { x: 101.059493946147, y: 0, z: 102.439695907393 },
    rotation: { x: 0, y: 65, z: 0 }, // 东南方向
    duration: 2000,
  },
  {
    timestamp: Date.now() + 11500,
    position: { x: 105.663718271762, y: 0, z: 99.7814457535891 },
    rotation: { x: 0, y: 55, z: 0 }, // 面向南方
    duration: 4000,
  },
  {
    timestamp: Date.now() + 15500,
    position: { x: 102.591898105678, y: 0, z: 101.932357389906 },
    rotation: { x: 0, y: 55, z: 0 }, // 西南方向
    duration: 3500,
  },
  {
    timestamp: Date.now() + 19000,
    position: { x: 106.664573623161, y: 0, z: 98.514976865281 },
    rotation: { x: 0, y: 45, z: 0 }, // 面向西方
    duration: 4000,
  },
  {
    timestamp: Date.now() + 23000,
    position: { x: 105.780690146678, y: 0, z: 99.3988603417642 },
    rotation: { x: 0, y: 45, z: 0 }, // 西北方向
    duration: 3000,
  },
  {
    timestamp: Date.now() + 26000,
    position: { x: 111.083991005577, y: 0, z: 104.702161200663 },
    rotation: { x: 0, y: 45, z: 0 }, // 面向北方
    duration: 3500,
  },
  {
    timestamp: Date.now() + 29500,
    position: { x: 107.548457099644, y: 0, z: 108.237695106596 },
    rotation: { x: 0, y: 45, z: 0 }, // 东北方向
    duration: 2500,
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 110.965837624269, y: 0, z: 104.165019589113 },
    rotation: { x: 0, y: 35, z: 0 }, // 面向东方停车
    duration: 3000,
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 108.097955442514, y: 0, z: 108.260779810558 },
    rotation: { x: 0, y: 35, z: 0 }, // 面向东方停车
    duration: 3000,
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 110.965837624269, y: 0, z: 104.165019589113 },
    rotation: { x: 0, y: 35, z: 0 }, // 面向东方停车
    duration: 3000,
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 110.289517079445, y: 0, z: 104.613408743907 },
    rotation: { x: 0, y: 27, z: 0 }, // 面向东方停车
    duration: 3000,
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 105.182123957375, y: 0, z: 114.637232141027 },
    rotation: { x: 0, y: 27, z: 0 }, // 面向东方停车
    duration: 3000,
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 107.839926792011, y: 0, z: 107.131828468912 },
    rotation: { x: 0, y: 12, z: 0 }, // 面向东方停车
    duration: 3000,
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 105.760809883833, y: 0, z: 116.91330447625 },
    rotation: { x: 0, y: 12, z: 0 }, // 面向东方停车
    duration: 3000,
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 106.427308061452, y: 0, z: 110.571997906308 },
    rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
    duration: 3000,
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 106.427308061452, y: 0, z: 129.321997906308 },
    rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
    duration: 3000,
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 118.927308061452, y: 0, z: 129.321997906308 },
    rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
    duration: 3000,
  },
];

const sampleRouteData = [
  {
    timestamp: Date.now(),
    position: { x: 100, y: 0, z: -120 },
    rotation: { x: 0, y: 90, z: 0 }, // 面向北方
    duration: 1000,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now(),
    position: { x: 100, y: 0, z: 25 },
    rotation: { x: 0, y: 90, z: 0 }, // 面向北方
    duration: 1000,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 1000,
    position: { x: 100, y: 0, z: 68 },
    rotation: { x: 0, y: 90, z: 0 }, // 继续向北
    duration: 3000,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 1000,
    position: { x: 100, y: 0, z: 68 },
    rotation: { x: 0, y: 145, z: 0 }, // 继续向北
    duration: 7000,
    meshName: "履带1" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 4000,
    position: { x: 85, y: 0, z: 68 },
    rotation: { x: 0, y: 90, z: 0 }, // 东北方向
    duration: 2500,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 6500,
    position: { x: 85, y: 0, z: 68 },
    rotation: { x: 0, y: 65, z: 0 }, // 面向东方
    duration: 3000,
    meshName: "履带1" // 指定mesh旋转
  },
  {
    timestamp: Date.now() + 9500,
    position: { x: 88.1696369630552, y: 0, z: 74.7973084027749 },
    rotation: { x: 0, y: 65, z: 0 }, // 东南方向
    duration: 2000,
    meshName: "履带1" // 指定mesh旋转
  },
  {
    timestamp: Date.now() + 11500,
    position: { x: 88.1696369630552, y: 0, z: 74.7973084027749 },
    rotation: { x: 0, y: 55, z: 0 }, // 面向南方
    duration: 4000,
    meshName: "履带2" // 指定mesh旋转
  },
  {
    timestamp: Date.now() + 15500,
    position: { x: 85.0978167969715, y: 0, z: 76.9482200390913 },
    rotation: { x: 0, y: 55, z: 0 }, // 西南方向
    duration: 3500,
    meshName: "履带2" // 指定mesh旋转
  },
  {
    timestamp: Date.now() + 19000,
    position: { x: 85.0978167969715, y: 0, z: 76.9482200390913 },
    rotation: { x: 0, y: 45, z: 0 }, // 面向西方
    duration: 4000,
    meshName: "转台" // 指定mesh旋转
  },
  {
    timestamp: Date.now() + 23000,
    position: { x: 84.2139333204883, y: 0, z: 77.8321035155745 },
    rotation: { x: 0, y: 45, z: 0 }, // 西北方向
    duration: 3000,
    meshName: "转台" // 指定mesh旋转
  },
  {
    timestamp: Date.now() + 26000,
    position: { x: 89.5172341793874, y: 0, z: 83.1354043744736 },
    rotation: { x: 0, y: 45, z: 0 }, // 面向北方
    duration: 3500,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 29500,
    position: { x: 85.9817002734547, y: 0, z: 86.6709382804063 },
    rotation: { x: 0, y: 45, z: 0 }, // 东北方向
    duration: 2500,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 85.9817002734547, y: 0, z: 86.6709382804063 },
    rotation: { x: 0, y: 35, z: 0 }, // 面向东方停车
    duration: 3000,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 83.1138180916995, y: 0, z: 90.7666985018513 },
    rotation: { x: 0, y: 35, z: 0 }, // 面向东方停车
    duration: 3000,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 83.1138180916995, y: 0, z: 90.7666985018513 },
    rotation: { x: 0, y: 27, z: 0 }, // 面向东方停车
    duration: 3000,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 78.0064249696296, y: 0, z: 100.79052189897 },
    rotation: { x: 0, y: 27, z: 0 }, // 面向东方停车
    duration: 3000,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 78.0064249696296, y: 0, z: 100.79052189897 },
    rotation: { x: 0, y: 12, z: 0 }, // 面向东方停车
    duration: 3000,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 75.927308061452, y: 0, z: 110.571997906308 },
    rotation: { x: 0, y: 12, z: 0 }, // 面向东方停车
    duration: 3000,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 75.927308061452, y: 0, z: 110.571997906308 },
    rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
    duration: 3000,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 75.927308061452, y: 0, z: 115.321997906308 },
    rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
    duration: 3000,
    meshName: "body" // 整体模型旋转
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 118.427308061452, y: 0, z: 115.321997906308 },
    rotation: { x: 0, y: 0, z: 0 }, // 面向东方停车
    duration: 3000,
    meshName: "body" // 整体模型旋转
  },
];

// 排布模拟数据（更复杂的汽车路径）
const cityRouteData = [
  // 起点：市中心
  { position: { x: 0, y: 0, z: 0 }, rotation: { x: 0, y: 0, z: 0 }, duration: 1000 },
  // 第一段：向北到商业区
  { position: { x: 0, y: 0, z: -60 }, rotation: { x: 0, y: 0, z: 0 }, duration: 2500 },
  { position: { x: 20, y: 0, z: -100 }, rotation: { x: 0, y: 30, z: 0 }, duration: 2000 },
  { position: { x: 60, y: 0, z: -120 }, rotation: { x: 0, y: 60, z: 0 }, duration: 2000 },
  { position: { x: 100, y: 0, z: -130 }, rotation: { x: 0, y: 80, z: 0 }, duration: 2000 },
  { position: { x: 140, y: 0, z: -125 }, rotation: { x: 0, y: 90, z: 0 }, duration: 2000 },
  // 第二段：东部工业区
  { position: { x: 170, y: 0, z: -100 }, rotation: { x: 0, y: 120, z: 0 }, duration: 1800 },
  { position: { x: 185, y: 0, z: -60 }, rotation: { x: 0, y: 150, z: 0 }, duration: 1800 },
  { position: { x: 190, y: 0, z: -20 }, rotation: { x: 0, y: 170, z: 0 }, duration: 1800 },
  { position: { x: 185, y: 0, z: 20 }, rotation: { x: 0, y: 180, z: 0 }, duration: 1800 },
  // 第三段：南部住宅区
  { position: { x: 170, y: 0, z: 60 }, rotation: { x: 0, y: 200, z: 0 }, duration: 2200 },
  { position: { x: 140, y: 0, z: 100 }, rotation: { x: 0, y: 220, z: 0 }, duration: 2200 },
  { position: { x: 100, y: 0, z: 130 }, rotation: { x: 0, y: 240, z: 0 }, duration: 2200 },
  { position: { x: 60, y: 0, z: 150 }, rotation: { x: 0, y: 260, z: 0 }, duration: 2200 },
  { position: { x: 20, y: 0, z: 160 }, rotation: { x: 0, y: 270, z: 0 }, duration: 2200 },
  // 第四段：西部公园区
  { position: { x: -20, y: 0, z: 155 }, rotation: { x: 0, y: 290, z: 0 }, duration: 2000 },
  { position: { x: -60, y: 0, z: 140 }, rotation: { x: 0, y: 310, z: 0 }, duration: 2000 },
  { position: { x: -100, y: 0, z: 115 }, rotation: { x: 0, y: 320, z: 0 }, duration: 2000 },
  { position: { x: -140, y: 0, z: 80 }, rotation: { x: 0, y: 330, z: 0 }, duration: 2000 },
  { position: { x: -170, y: 0, z: 40 }, rotation: { x: 0, y: 340, z: 0 }, duration: 2000 },
  { position: { x: -185, y: 0, z: 0 }, rotation: { x: 0, y: 350, z: 0 }, duration: 2000 },
  // 第五段：西北角到北部
  { position: { x: -180, y: 0, z: -40 }, rotation: { x: 0, y: 0, z: 0 }, duration: 2000 },
  { position: { x: -160, y: 0, z: -80 }, rotation: { x: 0, y: 20, z: 0 }, duration: 2000 },
  { position: { x: -130, y: 0, z: -110 }, rotation: { x: 0, y: 40, z: 0 }, duration: 2000 },
  { position: { x: -90, y: 0, z: -130 }, rotation: { x: 0, y: 60, z: 0 }, duration: 2000 },
  { position: { x: -50, y: 0, z: -140 }, rotation: { x: 0, y: 80, z: 0 }, duration: 2000 },
  // 最终回到市中心
  { position: { x: -20, y: 0, z: -120 }, rotation: { x: 0, y: 100, z: 0 }, duration: 1800 },
  { position: { x: 0, y: 0, z: -80 }, rotation: { x: 0, y: 120, z: 0 }, duration: 1800 },
  { position: { x: 10, y: 0, z: -40 }, rotation: { x: 0, y: 150, z: 0 }, duration: 1800 },
  { position: { x: 5, y: 0, z: -10 }, rotation: { x: 0, y: 170, z: 0 }, duration: 1500 },
  { position: { x: 0, y: 0, z: 0 }, rotation: { x: 0, y: 180, z: 0 }, duration: 2000 },
];

// 能耗模拟路线数据（大环形路径）
const circularRouteData = [
  // 起点：中心
  { position: { x: 0, y: 0, z: 0 }, rotation: { x: 0, y: 0, z: 0 }, duration: 1000 },
  // 第一段：向北移动到环形起点
  { position: { x: 0, y: 0, z: -100 }, rotation: { x: 0, y: 0, z: 0 }, duration: 2000 },
  // 开始环形路径 - 北部弧线（从北向东）
  { position: { x: 20, y: 0, z: -98 }, rotation: { x: 0, y: 15, z: 0 }, duration: 1500 },
  { position: { x: 39, y: 0, z: -92 }, rotation: { x: 0, y: 30, z: 0 }, duration: 1500 },
  { position: { x: 56, y: 0, z: -83 }, rotation: { x: 0, y: 45, z: 0 }, duration: 1500 },
  { position: { x: 71, y: 0, z: -71 }, rotation: { x: 0, y: 60, z: 0 }, duration: 1500 },
  { position: { x: 83, y: 0, z: -56 }, rotation: { x: 0, y: 75, z: 0 }, duration: 1500 },
  { position: { x: 92, y: 0, z: -39 }, rotation: { x: 0, y: 90, z: 0 }, duration: 1500 },
  // 东部弧线（从北向南）
  { position: { x: 98, y: 0, z: -20 }, rotation: { x: 0, y: 105, z: 0 }, duration: 1500 },
  { position: { x: 100, y: 0, z: 0 }, rotation: { x: 0, y: 120, z: 0 }, duration: 1500 },
  { position: { x: 98, y: 0, z: 20 }, rotation: { x: 0, y: 135, z: 0 }, duration: 1500 },
  { position: { x: 92, y: 0, z: 39 }, rotation: { x: 0, y: 150, z: 0 }, duration: 1500 },
  { position: { x: 83, y: 0, z: 56 }, rotation: { x: 0, y: 165, z: 0 }, duration: 1500 },
  { position: { x: 71, y: 0, z: 71 }, rotation: { x: 0, y: 180, z: 0 }, duration: 1500 },
  // 南部弧线（从东向西）
  { position: { x: 56, y: 0, z: 83 }, rotation: { x: 0, y: 195, z: 0 }, duration: 1500 },
  { position: { x: 39, y: 0, z: 92 }, rotation: { x: 0, y: 210, z: 0 }, duration: 1500 },
  { position: { x: 20, y: 0, z: 98 }, rotation: { x: 0, y: 225, z: 0 }, duration: 1500 },
  { position: { x: 0, y: 0, z: 100 }, rotation: { x: 0, y: 240, z: 0 }, duration: 1500 },
  { position: { x: -20, y: 0, z: 98 }, rotation: { x: 0, y: 255, z: 0 }, duration: 1500 },
  { position: { x: -39, y: 0, z: 92 }, rotation: { x: 0, y: 270, z: 0 }, duration: 1500 },
  // 西部弧线（从南向北）
  { position: { x: -56, y: 0, z: 83 }, rotation: { x: 0, y: 285, z: 0 }, duration: 1500 },
  { position: { x: -71, y: 0, z: 71 }, rotation: { x: 0, y: 300, z: 0 }, duration: 1500 },
  { position: { x: -83, y: 0, z: 56 }, rotation: { x: 0, y: 315, z: 0 }, duration: 1500 },
  { position: { x: -92, y: 0, z: 39 }, rotation: { x: 0, y: 330, z: 0 }, duration: 1500 },
  { position: { x: -98, y: 0, z: 20 }, rotation: { x: 0, y: 345, z: 0 }, duration: 1500 },
  { position: { x: -100, y: 0, z: 0 }, rotation: { x: 0, y: 0, z: 0 }, duration: 1500 },
  // 完成环形 - 西北部弧线（回到北部）
  { position: { x: -98, y: 0, z: -20 }, rotation: { x: 0, y: 15, z: 0 }, duration: 1500 },
  { position: { x: -92, y: 0, z: -39 }, rotation: { x: 0, y: 30, z: 0 }, duration: 1500 },
  { position: { x: -83, y: 0, z: -56 }, rotation: { x: 0, y: 45, z: 0 }, duration: 1500 },
  { position: { x: -71, y: 0, z: -71 }, rotation: { x: 0, y: 60, z: 0 }, duration: 1500 },
  { position: { x: -56, y: 0, z: -83 }, rotation: { x: 0, y: 75, z: 0 }, duration: 1500 },
  { position: { x: -39, y: 0, z: -92 }, rotation: { x: 0, y: 90, z: 0 }, duration: 1500 },
  { position: { x: -20, y: 0, z: -98 }, rotation: { x: 0, y: 105, z: 0 }, duration: 1500 },
  { position: { x: 0, y: 0, z: -100 }, rotation: { x: 0, y: 120, z: 0 }, duration: 1500 },
  // 回到中心
  { position: { x: 0, y: 0, z: -50 }, rotation: { x: 0, y: 180, z: 0 }, duration: 2000 },
  { position: { x: 0, y: 0, z: 0 }, rotation: { x: 0, y: 180, z: 0 }, duration: 2000 },
];

// 简单感知模拟路线数据
const forwardBackwardRouteData = [
  {
    timestamp: Date.now() + 32000,
    position: { x: -116.927308061452, y: 0, z: 115 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 0
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: -115.927308061452, y: 0, z: 115 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 1000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 45.927308061452, y: 0, z: 115 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 10000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 47.927308061452, y: 0, z: 115 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 2000
  },
  {
    timestamp: Date.now() + 32000,
    position: { x: 116.927308061452, y: 0, z: 115 },
    rotation: { x: 0, y: 0, z: 0 },   // 面向东方停车
    duration: 10000
  },
];

// 方法
const setDataSource = source => {
  dataSource.value = source;
  if (simulator.isRunning) {
    stopRoute();
  }
};

const loadSampleData = () => {
  jsonInput.value = JSON.stringify(sampleRouteData, null, 2);
};

// 加载排布模拟数据
const loadCityRouteData = () => {
  jsonInput.value = JSON.stringify(cityRouteData, null, 2);
};

// 加载能耗模拟路线数据
const loadCircularRouteData = () => {
  jsonInput.value = JSON.stringify(circularRouteData, null, 2);
};

// 加载感知模拟路线数据
const loadForwardBackwardRouteData = () => {
  jsonInput.value = JSON.stringify(forwardBackwardRouteData, null, 2);
};

const loadJsonData = () => {
  try {
    const data = JSON.parse(jsonInput.value);
    if (simulator.loadRouteData(data)) {
      updateRouteStatus();
      console.log('Route data loaded successfully');
    }
  } catch (error) {
    alert('JSON格式错误: ' + error.message);
  }
};

const toggleWebSocket = () => {
  if (websocketConnected.value) {
    // 断开连接
    stopStatusSending(); // 停止状态发送
    simulator.disconnect();
    websocketConnected.value = false;
    console.log('🔌 WebSocket 连接已断开');
  } else {
    // 建立连接
    console.log('🔗 正在连接 WebSocket:', websocketUrl.value);

    // 设置连接状态监听
    const originalOnOpen = simulator.websocket?.onopen;
    const originalOnClose = simulator.websocket?.onclose;
    const originalOnError = simulator.websocket?.onerror;

    if (simulator.connectWebSocket(websocketUrl.value)) {
      // 重写事件处理器以更新连接状态
      if (simulator.websocket) {
        simulator.websocket.onopen = (event) => {
          websocketConnected.value = true;
          console.log('✅ WebSocket 连接成功');
          startStatusSending(); // 开始状态发送
          if (originalOnOpen) originalOnOpen(event);
        };

        simulator.websocket.onclose = (event) => {
          websocketConnected.value = false;
          stopStatusSending(); // 停止状态发送
          console.log('🔌 WebSocket 连接已关闭');
          if (originalOnClose) originalOnClose(event);
        };

        simulator.websocket.onerror = (event) => {
          websocketConnected.value = false;
          stopStatusSending(); // 停止状态发送
          console.error('WebSocket 连接错误');
          if (originalOnError) originalOnError(event);
        };
      }
    } else {
      console.error('WebSocket 连接失败');
    }
  }
};

// WebSocket 控制方法
const sendTestMessage = () => {
  if (simulator.websocket && websocketConnected.value) {
    simulator.sendWebSocketMessage({
      type: 'test',
      action: 'ping',
      data: {
        message: '这是一条测试消息',
        timestamp: Date.now(),
        clientId: 'route_simulator_' + Math.random().toString(36).substr(2, 9)
      }
    });
    console.log('已发送测试消息');
  } else {
    console.warn('WebSocket 未连接，无法发送测试消息');
  }
};

const requestRouteData = () => {
  if (simulator.websocket && websocketConnected.value) {
    simulator.sendWebSocketMessage({
      type: 'request',
      action: 'route_data',
      data: {
        requestType: 'batch',
        format: 'json',
        timestamp: Date.now()
      }
    });
    console.log('已请求路线数据');
  } else {
    console.warn('WebSocket 未连接，无法请求路线数据');
  }
};

// WebSocket 状态发送定时器
let statusSendTimer = null;

// 开始发送状态信息
const startStatusSending = () => {
  if (statusSendTimer) {
    clearInterval(statusSendTimer);
  }

  statusSendTimer = setInterval(() => {
    sendVehicleStatus();
  }, 1000); // 每秒发送一次

  console.log('开始定时发送车辆状态信息（每秒一次）');
};

// 停止发送状态信息
const stopStatusSending = () => {
  if (statusSendTimer) {
    clearInterval(statusSendTimer);
    statusSendTimer = null;
    console.log('停止发送车辆状态信息');
  }
};

// 发送车辆状态信息
const sendVehicleStatus = () => {
  if (!simulator.websocket || !websocketConnected.value) {
    return;
  }

  // 获取所有车辆的状态信息
  const vehicleStatusList = getAllVehicleStatus();

  if (vehicleStatusList.length > 0) {
    simulator.sendWebSocketMessage(vehicleStatusList);
    console.log('发送车辆状态信息:', vehicleStatusList.length, '辆车');
  }
};

// 获取车辆类型代码
const getVehicleTypeCode = (model) => {
  // 1. 优先使用模型的type字段（新的车辆类型代码）
  if (model.type) {
    return model.type;
  }

  // 2. 从设备ID中提取类型代码（新格式：CODE_12345）
  if (model.id && model.id.includes('_')) {
    const typeCode = model.id.split('_')[0];
    // 验证是否为有效的车辆类型代码
    const validTypeCodes = ['MBC', 'BBC', 'TBC', 'BWE', 'RSC', 'PCC', 'RSCM', 'BWEM', 'MDF'];
    if (validTypeCodes.includes(typeCode)) {
      return typeCode;
    }
  }

  // 3. 使用deviceType字段映射
  if (model.deviceType) {
    const deviceTypeMap = {
      'mobile_loader': 'MBC',           // 移动转载机
      'bridge_loader': 'BBC',           // 桥式转载机
      'relay_loader': 'TBC',            // 中继转载机
      'excavator_3500t': 'BWE',         // 3500T斗轮挖掘机
      'large_spreader': 'RSC',          // 大型布料机
      'mobile_power_vehicle': 'PCC',    // 移动供电车
      'fan_spreader': 'RSCM',           // 扇形布料机
      'excavator_1500t': 'BWEM',        // 1500T斗轮挖掘机
      'mobile_distribution_funnel': 'MDF' // 移动分料漏斗
    };

    if (deviceTypeMap[model.deviceType]) {
      return deviceTypeMap[model.deviceType];
    }
  }

  // 4. 使用模型路径映射
  if (model.path) {
    const pathTypeMap = {
      '/models/new/移动转载机ok.glb': 'MBC',
      '/models/new/桥式转载机ok.glb': 'BBC',
      '/models/new/中继转载机-B-ok.glb': 'TBC',
      '/models/new/3500T斗轮挖掘机.glb': 'BWE',
      '/models/new/大型布料机ok-6.glb': 'RSC',
      '/models/new/移动供电车ok.glb': 'PCC',
      '/models/new/扇形布料机ok.glb': 'RSCM',
      '/models/new/1500T斗轮挖掘机.glb': 'BWEM',
      // 旧路径兼容
      '/models/移动转载机ok.glb': 'MBC',
      '/models/桥式转载机ok.glb': 'BBC',
      '/models/中继转载机-B-ok.glb': 'TBC',
      '/models/3500T斗轮挖掘机.glb': 'BWE',
      '/models/大型布料机ok-6.glb': 'RSC',
      '/models/扇形布料机ok.glb': 'RSCM',
      '/models/1500T斗轮挖掘机.glb': 'BWEM'
    };

    if (pathTypeMap[model.path]) {
      return pathTypeMap[model.path];
    }
  }

  // 5. 使用模型名称映射
  if (model.name) {
    const nameTypeMap = {
      '移动转载机': 'MBC',
      '桥式转载机': 'BBC',
      '中继转载机': 'TBC',
      '3500T斗轮挖掘机': 'BWE',
      '大型布料机': 'RSC',
      '移动供电车': 'PCC',
      '扇形布料机': 'RSCM',
      '1500T斗轮挖掘机': 'BWEM',
      '移动分料漏斗': 'MDF'
    };

    for (const [name, code] of Object.entries(nameTypeMap)) {
      if (model.name.includes(name)) {
        return code;
      }
    }
  }

  // 默认返回移动转载机类型
  return 'MBC';
};

// 获取所有车辆状态
const getAllVehicleStatus = () => {
  const statusList = [];

  // 从父组件获取所有模型数据
  if (props.models && Array.isArray(props.models)) {
    props.models.forEach(model => {
      const status = {
        device_id: model.id,
        position: {
          x: Math.round(model.currentPosition?.x || model.initialPosition?.x || 0),
          y: Math.round(model.currentPosition?.y || model.initialPosition?.y || 0),
          z: Math.round(model.currentPosition?.z || model.initialPosition?.z || 0)
        },
        rotation: {
          x: Math.round(model.currentRotation?.x || model.initialRotation?.x || 0),
          y: Math.round(model.currentRotation?.y || model.initialRotation?.y || 0),
          z: Math.round(model.currentRotation?.z || model.initialRotation?.z || 0)
        },
        duration: 0,
        power: Math.round(model.battery || 100),
        consume: Math.round(model.consume || 0),
        type: getVehicleTypeCode(model) // 添加车辆类型代码字段
      };

      statusList.push(status);
    });
  }

  return statusList;
};

const startRoute = () => {
  console.log('Starting route...');
  console.log('Current model:', simulator.model);
  console.log('ModelComponent:', simulator.modelComponent);
  console.log('Route data length:', simulator.routeData.length);
  console.log('Can start:', canStart.value);
  console.log('Data source:', dataSource.value);

  // 详细检查启动条件
  if (!simulator.model) {
    console.error('No model available');
    alert('无法开始路线：未找到模型对象。请确保已选择模型并且模型已加载完成。');
    return;
  }

  if (dataSource.value === 'json' && simulator.routeData.length === 0) {
    console.error('No route data available');
    alert('无法开始路线：未加载路线数据。请点击"加载路线数据"按钮。');
    return;
  }

  if (dataSource.value === 'websocket' && !websocketConnected.value) {
    console.error('WebSocket not connected');
    alert('无法开始路线：WebSocket未连接。请检查连接状态。');
    return;
  }

  if (simulator.start()) {
    updateRouteStatus();
    console.log('Route started successfully');
  } else {
    console.error('Failed to start route - simulator.start() returned false');
    alert('路线启动失败：模拟器内部错误。请检查控制台日志。');
  }
};

const pauseRoute = () => {
  simulator.pause();
  updateRouteStatus();
};

const resumeRoute = () => {
  simulator.resume();
  updateRouteStatus();
};

const stopRoute = () => {
  simulator.stop();
  updateRouteStatus();
};

const updatePlaybackSpeed = () => {
  simulator.options.speed = playbackSpeed.value;
};

const updateInterpolationMode = () => {
  simulator.options.interpolationMode = interpolationMode.value;
};

const updateLoopMode = () => {
  simulator.options.loop = loopPlayback.value;
};

const updateSmoothRotation = () => {
  simulator.options.smoothRotation = smoothRotation.value;
};

const updateRouteStatus = () => {
  const status = simulator.getStatus();
  Object.assign(routeStatus, status);
  emit('status-change', status);
};

const getStatusClass = () => {
  if (routeStatus.isRunning && !routeStatus.isPaused) return 'running';
  if (routeStatus.isPaused) return 'paused';
  return 'stopped';
};

const getStatusText = () => {
  if (routeStatus.isRunning && !routeStatus.isPaused) return '运行中';
  if (routeStatus.isPaused) return '已暂停';
  return '已停止';
};

// 切换面板展开/关闭状态
const togglePanel = () => {
  isExpanded.value = !isExpanded.value;
};

// 生命周期
onMounted(() => {
  // 设置模型
  console.log('RouteController mounted, received model:', props.model);
  console.log('RouteController mounted, received modelComponent:', props.modelComponent);
  console.log('RouteController mounted, received models:', props.models);
  console.log('RouteController mounted, received sceneManager:', props.sceneManager);

  if (props.model) {
    simulator.setModel(props.model);
    console.log('Model set in simulator');
  }

  if (props.modelComponent) {
    simulator.setModelComponent(props.modelComponent);
    console.log('ModelComponent set in simulator');
  }

  if (props.models) {
    simulator.setAllModels(props.models);
    console.log('All models set in simulator');
  }

  if (props.sceneManager) {
    simulator.setSceneManager(props.sceneManager);
    console.log('Scene manager set in simulator');
  }

  // 设置回调
  simulator.onPositionUpdate = data => {
    if (data.position) {
      currentPosition.value = data.position;
    }
    if (data.rotation) {
      currentRotation.value = data.rotation;
    }
    emit('position-update', data);
  };

  simulator.onRouteComplete = () => {
    updateRouteStatus();
    console.log('Route completed');
  };

  simulator.onError = error => {
    console.error('Route simulator error:', error);
  };

  // 加载示例数据
  loadSampleData();
});

onBeforeUnmount(() => {
  stopStatusSending(); // 清理状态发送定时器
  simulator.destroy();
});

// 监听模型变化
watch(
  () => props.model,
  (newModel, oldModel) => {
    console.log('Model prop changed:', { old: oldModel, new: newModel });
    if (newModel) {
      simulator.setModel(newModel);
      console.log('Updated model in simulator');
    } else {
      console.log('Model prop is null/undefined');
    }
  },
  { immediate: true },
);

// 监听ModelComponent变化
watch(
  () => props.modelComponent,
  (newModelComponent, oldModelComponent) => {
    console.log('ModelComponent prop changed:', { old: oldModelComponent, new: newModelComponent });
    if (newModelComponent) {
      simulator.setModelComponent(newModelComponent);
      console.log('Updated ModelComponent in simulator');
    } else {
      console.log('ModelComponent prop is null/undefined');
    }
  },
  { immediate: true },
);

// 监听models变化
watch(
  () => props.models,
  (newModels, oldModels) => {
    console.log('Models prop changed:', { old: oldModels?.length, new: newModels?.length });
    if (newModels) {
      simulator.setAllModels(newModels);
      console.log('Updated all models in simulator');
    }
  },
  { immediate: true },
);

// 监听sceneManager变化
watch(
  () => props.sceneManager,
  (newSceneManager, oldSceneManager) => {
    console.log('SceneManager prop changed:', { old: !!oldSceneManager, new: !!newSceneManager });
    if (newSceneManager) {
      simulator.setSceneManager(newSceneManager);
      console.log('Updated scene manager in simulator');
    }
  },
  { immediate: true },
);
</script>

<style scoped>
/* Only keeping essential clip-path styles that can't be done with Tailwind */
.futuristic-card-symmetric {
  clip-path: polygon(1.5rem 0, 100% 0, 100% calc(100% - 1.5rem), calc(100% - 1.5rem) 100%, 0 100%, 0 1.5rem);
}

.futuristic-input-symmetric {
  clip-path: polygon(0.5rem 0, 100% 0, 100% calc(100% - 0.5rem), calc(100% - 0.5rem) 100%, 0 100%, 0 0.5rem);
}

.control-btn-symmetric {
  clip-path: polygon(0.75rem 0, 100% 0, 100% calc(100% - 0.75rem), calc(100% - 0.75rem) 100%, 0 100%, 0 0.75rem);
}

/* Custom slider styling */
.slider-thumb::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  cursor: pointer;
  border: 2px solid #0891b2;
  box-shadow: 0 0 10px rgba(6, 182, 212, 0.5);
}

.slider-thumb::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  cursor: pointer;
  border: 2px solid #0891b2;
  box-shadow: 0 0 10px rgba(6, 182, 212, 0.5);
}
</style>
