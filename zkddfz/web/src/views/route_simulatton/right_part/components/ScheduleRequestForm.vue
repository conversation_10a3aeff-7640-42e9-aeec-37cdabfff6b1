<script setup lang="ts">
import { ref, computed, watch } from 'vue';

// 定义接口
interface Coordinate {
  x: number;
  z: number;
}

interface ScheduleRequest {
  processType: number; // 1转储，2装船，3卸船分料
  // 转储相关坐标
  fabricStartPoint?: Coordinate;
  fabricEndPoint?: Coordinate;
  pickupStartPoint?: Coordinate;
  pickupEndPoint?: Coordinate;
  // 卸船分料相关坐标
  unloadPickupPoint?: Coordinate; // 取料点
  firstTeamFabricStart?: Coordinate;
  firstTeamFabricEnd?: Coordinate;
  secondTeamFabricStart?: Coordinate;
  secondTeamFabricEnd?: Coordinate;
  // 装船相关坐标
  shipPickupStart?: Coordinate;
  shipPickupEnd?: Coordinate;
  shipFabricEnd?: Coordinate;
}

// Props
const props = defineProps<{
  sceneManagerRef?: any;
}>();

// Emits
const emit = defineEmits<{
  'coordinate-request': [type: string, processType: number];
  'schedule-submit': [request: ScheduleRequest];
}>();

// 响应式数据
const scheduleRequest = ref<ScheduleRequest>({
  fabricDirection: 0,
  processType: 1,
});

// 当前等待设置的坐标类型
const waitingForCoordinate = ref<string | null>(null);

// 当前正在设置的坐标类型（从右击菜单传入）
const currentSettingCoordinate = ref<string | null>(null);

// 工艺类型选项
const processTypeOptions = [
  { value: 1, label: '转储' },
  { value: 2, label: '装船' },
  { value: 3, label: '卸船分料' }
];



// 计算属性：根据工艺类型显示不同的坐标字段
const coordinateFields = computed(() => {
  const fields = [];
  
  switch (scheduleRequest.value.processType) {
    case 1: // 转储
      fields.push(
        { key: 'fabricStartPoint', label: '布料起点', required: true },
        { key: 'fabricEndPoint', label: '布料终点', required: true },
        { key: 'pickupStartPoint', label: '取料起点', required: true },
        { key: 'pickupEndPoint', label: '取料终点', required: true }
      );
      break;
    case 2: // 装船
      fields.push(
        { key: 'shipPickupStart', label: '取料起点', required: true },
        { key: 'shipPickupEnd', label: '取料终点', required: true },
        { key: 'shipFabricEnd', label: '布料终点', required: true }
      );
      break;
    case 3: // 卸船分料
      fields.push(
        { key: 'unloadPickupPoint', label: '取料点', required: true },
        { key: 'firstTeamFabricStart', label: '第一队布料起点', required: true },
        { key: 'firstTeamFabricEnd', label: '第一队布料终点', required: true },
        { key: 'secondTeamFabricStart', label: '第二队布料起点', required: true },
        { key: 'secondTeamFabricEnd', label: '第二队布料终点', required: true }
      );
      break;
  }
  
  return fields;
});

// 通知父组件工艺类型变化，启用右击功能
const notifyProcessTypeChange = () => {
  emit('coordinate-request', 'process-type-changed', scheduleRequest.value.processType);
};

// 监听工艺类型变化，清空相关坐标并通知父组件
watch(() => scheduleRequest.value.processType, (newType, oldType) => {
  if (newType !== oldType) {
    // 清空所有坐标
    scheduleRequest.value.fabricStartPoint = undefined;
    scheduleRequest.value.fabricEndPoint = undefined;
    scheduleRequest.value.pickupStartPoint = undefined;
    scheduleRequest.value.pickupEndPoint = undefined;
    scheduleRequest.value.unloadPickupPoint = undefined;
    scheduleRequest.value.firstTeamFabricStart = undefined;
    scheduleRequest.value.firstTeamFabricEnd = undefined;
    scheduleRequest.value.secondTeamFabricStart = undefined;
    scheduleRequest.value.secondTeamFabricEnd = undefined;
    scheduleRequest.value.shipPickupStart = undefined;
    scheduleRequest.value.shipPickupEnd = undefined;
    scheduleRequest.value.shipFabricEnd = undefined;

    // 清空等待状态
    waitingForCoordinate.value = null;

    // 通知父组件工艺类型变化
    notifyProcessTypeChange();
  }
}, { immediate: true });

// 设置坐标（从父组件调用）
const setCoordinate = (coordinateType: string, coordinate: Coordinate) => {
  // 如果没有指定坐标类型，使用当前正在设置的坐标类型
  const targetCoordinateType = coordinateType || currentSettingCoordinate.value;

  if (targetCoordinateType && scheduleRequest.value.hasOwnProperty(targetCoordinateType)) {
    (scheduleRequest.value as any)[targetCoordinateType] = coordinate;
    console.log(`✅ 坐标已设置: ${targetCoordinateType}`, coordinate);

    // 强制触发响应式更新
    scheduleRequest.value = { ...scheduleRequest.value };
  } else {
    console.warn(`❌ 无效的坐标类型: ${targetCoordinateType}`);
  }

  // 清空等待状态
  waitingForCoordinate.value = null;
  currentSettingCoordinate.value = null;
};

// 设置当前正在设置的坐标类型（从主页面调用）
const setCurrentSettingCoordinate = (coordinateType: string) => {
  currentSettingCoordinate.value = coordinateType;
  console.log(`📍 准备设置坐标类型: ${coordinateType}`);
};

// 手动输入坐标
const updateCoordinate = (coordinateType: string, axis: 'x' | 'z', value: number) => {
  if (!scheduleRequest.value[coordinateType as keyof ScheduleRequest]) {
    (scheduleRequest.value as any)[coordinateType] = { x: 0, z: 0 };
  }
  ((scheduleRequest.value as any)[coordinateType] as Coordinate)[axis] = value;
};

// 清除坐标
const clearCoordinate = (coordinateType: string) => {
  (scheduleRequest.value as any)[coordinateType] = undefined;

  // 通知父组件删除场景中的小旗
  emit('coordinate-request', 'clear-flag', coordinateType);
};

// 验证表单
const validateForm = (): boolean => {
  for (const field of coordinateFields.value) {
    if (field.required && !scheduleRequest.value[field.key as keyof ScheduleRequest]) {
      return false;
    }
  }
  return true;
};



// 提交调度请求
const submitRequest = () => {
  if (!validateForm()) {
    alert('请填写所有必需的坐标点');
    return;
  }

  // 模拟返回路线电文数据
  const mockRouteData = generateMockRouteData();

  emit('schedule-submit', {
    ...scheduleRequest.value,
    routeData: mockRouteData
  });

  // 重置表单
  scheduleRequest.value = {
    processType: 1,
  };
  waitingForCoordinate.value = null;
};

// 生成模拟路线电文数据
const generateMockRouteData = () => {
  // 完全按照path3.json的示例电文结构生成路线数据
  return [
    {
      "device_id": "device_26482",
      "path": [
        {
          "x": 0,
          "z": 0
        },
        {
          "x": 0,
          "z": -430
        },
        {
          "x": 700,
          "z": -430
        }
      ]
    },
    {
      "device_id": "device_67005",
      "path": [
        {
          "x": 200,
          "z": -200
        },
        {
          "x": 600,
          "z": -200
        },
        {
          "x": 600,
          "z": -800
        }
      ]
    }
  ];
};

// 暴露方法给父组件
defineExpose({
  setCoordinate,
  setCurrentSettingCoordinate
});
</script>

<template>
  <div class="card_box relative overflow-hidden group bg-gradient-to-br from-slate-800/80 to-slate-700/60 w-full">

    <div class="txt_title flex items-center justify-between">
      <h2 class="font-semibold">调度请求</h2>
    </div>

    <!-- Card Content -->
    <div class="relative z-10 p-4 pt-2">
      <div class="space-y-2">
        <!-- 布料方向选择 -->
        <div class="mb-2">
          <label class="block text-xs font-medium text-slate-400 mb-2">布料方向</label>

        </div>

        <!-- 工艺类型选择 -->
        <div class="mb-2">
          <label class="block text-xs font-medium text-slate-400 mb-2">工艺类型</label>
          <div class="relative">
            <select
              v-model="scheduleRequest.processType"
              class="futuristic-input-symmetric w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white text-sm appearance-none pr-8 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
            >
              <option
                v-for="option in processTypeOptions"
                :key="option.value"
                :value="option.value"
                class="bg-slate-800 text-white"
              >
                {{ option.label }}
              </option>
            </select>
            <span class="absolute right-3 top-1/2 -translate-y-1/2 text-cyan-400/70 pointer-events-none text-xs">▼</span>
          </div>
        </div>

        <!-- 坐标输入区域 -->
        <div class="mb-2">
          <div class="text-sm font-semibold text-white mb-3">坐标设置</div>

          <div
            v-for="field in coordinateFields"
            :key="field.key"
            class="mb-3 p-3 rounded border border-slate-600/50 hover:border-slate-500/70 transition-colors duration-200"
          >
            <div class="flex items-center justify-between">
              <span class="text-sm text-white font-medium">{{ field.label }}</span>
              <span v-if="field.required" class="text-xs text-red-400">*必填</span>
            </div>
            
            <!-- 坐标显示和操作 -->
            <div v-if="scheduleRequest[field.key as keyof ScheduleRequest]" class="space-y-2">
              <div class="text-xs text-green-400">
                坐标: [{{ (scheduleRequest[field.key as keyof ScheduleRequest] as Coordinate)?.x?.toFixed(1) || 0 }}, 
                {{ (scheduleRequest[field.key as keyof ScheduleRequest] as Coordinate)?.z?.toFixed(1) || 0 }}]
              </div>
              
              <!-- 手动调整坐标 -->
              <div class="grid grid-cols-2 gap-2">
                <div>
                  <label class="block text-xs font-medium text-slate-400 mb-1">X坐标</label>
                  <input
                    type="number"
                    step="0.1"
                    :value="(scheduleRequest[field.key as keyof ScheduleRequest] as Coordinate)?.x || 0"
                    @input="updateCoordinate(field.key, 'x', parseFloat(($event.target as HTMLInputElement).value))"
                    class="futuristic-input-symmetric w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white text-sm placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
                  />
                </div>
                <div>
                  <label class="block text-xs font-medium text-slate-400 mb-1">Z坐标</label>
                  <input
                    type="number"
                    step="0.1"
                    :value="(scheduleRequest[field.key as keyof ScheduleRequest] as Coordinate)?.z || 0"
                    @input="updateCoordinate(field.key, 'z', parseFloat(($event.target as HTMLInputElement).value))"
                    class="futuristic-input-symmetric w-full px-3 py-2 bg-slate-700/60 border border-cyan-500/30 text-white text-sm placeholder-slate-400 focus:border-cyan-400 focus:ring-1 focus:ring-cyan-400/50 focus:outline-none transition-all duration-200 hover:border-cyan-500/50"
                  />
                </div>
              </div>
              
              <button
                @click="clearCoordinate(field.key)"
                class="text-xs text-red-400 hover:text-red-300 transition-colors duration-200 px-3 py-1 rounded border border-red-500/30 hover:bg-red-500/20"
              >
                清除坐标
              </button>
            </div>
            
            <!-- 坐标状态显示 -->
            <div v-else class="text-center py-3 px-4 rounded border border-dashed border-cyan-400/50 bg-cyan-500/10">
              <div class="text-sm text-cyan-300 mb-1">🎯 右击3D场景设置坐标</div>
              <div class="text-xs text-slate-400">选择工艺类型后，右击场景即可设置此坐标点</div>
            </div>
          </div>
        </div>





        <!-- 提交按钮 -->
        <button
          @click="submitRequest"
          :disabled="!validateForm()"
          class="w-full px-4 py-3 text-sm font-medium rounded border transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed bg-gradient-to-r from-cyan-500/20 to-cyan-500/20 border-cyan-400 text-cyan-300 hover:from-cyan-500/30 hover:to-cyan-500/30 hover:shadow-lg hover:shadow-cyan-500/20"
        >
          📋 提交调度请求
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义滚动条样式 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
</style>
