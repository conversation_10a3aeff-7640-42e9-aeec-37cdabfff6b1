<template>
  <div class="render-quality-panel" :class="{ 'panel-collapsed': collapsed }">
    <!-- 面板标题 -->
    <div class="panel-header" @click="togglePanel">
      <div class="panel-title">
        <span class="icon">🎨</span>
        <span>渲染质量控制</span>
      </div>
      <div class="collapse-btn" :class="{ 'collapsed': collapsed }">
        <span>{{ collapsed ? '▶' : '▼' }}</span>
      </div>
    </div>

    <!-- 面板内容 -->
    <div class="panel-content" v-show="!collapsed">
      <!-- 基础设置 -->
      <div class="setting-group">
        <h4>基础设置</h4>
        
        <div class="setting-item">
          <label>
            <input 
              type="checkbox" 
              v-model="settings.enablePostProcessing"
              @change="updatePostProcessing"
            />
            启用后处理效果
          </label>
        </div>

        <div class="setting-item">
          <label>像素比例</label>
          <input 
            type="range" 
            min="0.5" 
            max="2" 
            step="0.1"
            v-model="settings.pixelRatio"
            @input="updatePixelRatio"
          />
          <span class="value">{{ settings.pixelRatio }}</span>
        </div>

        <div class="setting-item">
          <label>曝光度</label>
          <input 
            type="range" 
            min="0.1" 
            max="3" 
            step="0.1"
            v-model="settings.toneMappingExposure"
            @input="updateExposure"
          />
          <span class="value">{{ settings.toneMappingExposure }}</span>
        </div>
      </div>

      <!-- 后处理效果 -->
      <div class="setting-group" v-if="settings.enablePostProcessing">
        <h4>后处理效果</h4>
        
        <div class="setting-item">
          <label>
            <input 
              type="checkbox" 
              v-model="settings.enableFXAA"
              @change="updateFXAA"
            />
            FXAA抗锯齿
          </label>
        </div>

        <div class="setting-item">
          <label>
            <input
              type="checkbox"
              v-model="settings.enableSSAO"
              @change="updateSSAO"
              disabled
            />
            SSAO环境遮蔽 (开发中)
          </label>
        </div>

        <div class="setting-item">
          <label>
            <input
              type="checkbox"
              v-model="settings.enableBloom"
              @change="updateBloom"
              disabled
            />
            Bloom辉光效果 (开发中)
          </label>
        </div>
      </div>

      <!-- SSAO设置 -->
      <div class="setting-group" v-if="settings.enablePostProcessing && settings.enableSSAO">
        <h4>SSAO设置</h4>
        
        <div class="setting-item">
          <label>核心半径</label>
          <input 
            type="range" 
            min="1" 
            max="32" 
            step="1"
            v-model="ssaoSettings.kernelRadius"
            @input="updateSSAOSettings"
          />
          <span class="value">{{ ssaoSettings.kernelRadius }}</span>
        </div>

        <div class="setting-item">
          <label>核心大小</label>
          <input 
            type="range" 
            min="8" 
            max="64" 
            step="8"
            v-model="ssaoSettings.kernelSize"
            @input="updateSSAOSettings"
          />
          <span class="value">{{ ssaoSettings.kernelSize }}</span>
        </div>
      </div>

      <!-- Bloom设置 -->
      <div class="setting-group" v-if="settings.enablePostProcessing && settings.enableBloom">
        <h4>Bloom设置</h4>
        
        <div class="setting-item">
          <label>强度</label>
          <input 
            type="range" 
            min="0" 
            max="2" 
            step="0.1"
            v-model="bloomSettings.strength"
            @input="updateBloomSettings"
          />
          <span class="value">{{ bloomSettings.strength }}</span>
        </div>

        <div class="setting-item">
          <label>半径</label>
          <input 
            type="range" 
            min="0" 
            max="1" 
            step="0.1"
            v-model="bloomSettings.radius"
            @input="updateBloomSettings"
          />
          <span class="value">{{ bloomSettings.radius }}</span>
        </div>

        <div class="setting-item">
          <label>阈值</label>
          <input 
            type="range" 
            min="0" 
            max="2" 
            step="0.1"
            v-model="bloomSettings.threshold"
            @input="updateBloomSettings"
          />
          <span class="value">{{ bloomSettings.threshold }}</span>
        </div>
      </div>

      <!-- 预设按钮 -->
      <div class="setting-group">
        <h4>质量预设</h4>
        <div class="preset-buttons">
          <button @click="applyPreset('low')" class="preset-btn">低质量</button>
          <button @click="applyPreset('medium')" class="preset-btn">中等质量</button>
          <button @click="applyPreset('high')" class="preset-btn">高质量</button>
          <button @click="applyPreset('ultra')" class="preset-btn">超高质量</button>
        </div>
      </div>

      <!-- 性能信息 -->
      <div class="setting-group">
        <h4>性能信息</h4>
        <div class="performance-info">
          <div>FPS: {{ fps }}</div>
          <div>渲染时间: {{ renderTime }}ms</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';

const props = defineProps({
  sceneManager: {
    type: Object,
    required: true
  }
});

// 面板状态
const collapsed = ref(false);

// 渲染设置
const settings = reactive({
  enablePostProcessing: true,
  enableFXAA: true,
  enableSSAO: true,
  enableBloom: false,
  pixelRatio: Math.min(window.devicePixelRatio, 2),
  toneMappingExposure: 1.0
});

// SSAO设置
const ssaoSettings = reactive({
  kernelRadius: 8,
  kernelSize: 32
});

// Bloom设置
const bloomSettings = reactive({
  strength: 0.5,
  radius: 0.4,
  threshold: 0.85
});

// 性能监控
const fps = ref(0);
const renderTime = ref(0);
let frameCount = 0;
let lastTime = performance.now();
let performanceInterval = null;

// 切换面板展开/收起
const togglePanel = () => {
  collapsed.value = !collapsed.value;
};

// 更新后处理
const updatePostProcessing = () => {
  if (props.sceneManager?.togglePostProcessing) {
    props.sceneManager.togglePostProcessing(settings.enablePostProcessing);
  }
};

// 更新像素比例
const updatePixelRatio = () => {
  if (props.sceneManager?.updateRenderQuality) {
    props.sceneManager.updateRenderQuality({ pixelRatio: parseFloat(settings.pixelRatio) });
  }
};

// 更新曝光度
const updateExposure = () => {
  if (props.sceneManager?.updateRenderQuality) {
    props.sceneManager.updateRenderQuality({ toneMappingExposure: parseFloat(settings.toneMappingExposure) });
  }
};

// 更新FXAA
const updateFXAA = () => {
  if (props.sceneManager?.updateRenderQuality) {
    props.sceneManager.updateRenderQuality({ enableFXAA: settings.enableFXAA });
  }
};

// 更新SSAO
const updateSSAO = () => {
  if (props.sceneManager?.toggleSSAO) {
    props.sceneManager.toggleSSAO(settings.enableSSAO);
  }
};

// 更新Bloom
const updateBloom = () => {
  if (props.sceneManager?.toggleBloom) {
    props.sceneManager.toggleBloom(settings.enableBloom);
  }
};

// 更新SSAO设置
const updateSSAOSettings = () => {
  if (props.sceneManager?.updateSSAOSettings) {
    props.sceneManager.updateSSAOSettings(
      parseInt(ssaoSettings.kernelRadius),
      parseInt(ssaoSettings.kernelSize)
    );
  }
};

// 更新Bloom设置
const updateBloomSettings = () => {
  if (props.sceneManager?.updateBloomSettings) {
    props.sceneManager.updateBloomSettings(
      parseFloat(bloomSettings.strength),
      parseFloat(bloomSettings.radius),
      parseFloat(bloomSettings.threshold)
    );
  }
};

// 应用质量预设
const applyPreset = (preset) => {
  const presets = {
    low: {
      enablePostProcessing: false,
      enableFXAA: false,
      enableSSAO: false,
      enableBloom: false,
      pixelRatio: 0.5,
      toneMappingExposure: 1.0
    },
    medium: {
      enablePostProcessing: true,
      enableFXAA: true,
      enableSSAO: false,
      enableBloom: false,
      pixelRatio: 1.0,
      toneMappingExposure: 1.0
    },
    high: {
      enablePostProcessing: true,
      enableFXAA: true,
      enableSSAO: false, // 暂时禁用
      enableBloom: false,
      pixelRatio: 1.5,
      toneMappingExposure: 1.2
    },
    ultra: {
      enablePostProcessing: true,
      enableFXAA: true,
      enableSSAO: false, // 暂时禁用
      enableBloom: false, // 暂时禁用
      pixelRatio: 2.0,
      toneMappingExposure: 1.5
    }
  };

  const presetSettings = presets[preset];
  if (presetSettings) {
    Object.assign(settings, presetSettings);
    
    // 应用所有设置
    if (props.sceneManager?.updateRenderQuality) {
      props.sceneManager.updateRenderQuality(presetSettings);
    }
    
    console.log(`🎨 已应用${preset}质量预设`);
  }
};

// 性能监控
const startPerformanceMonitoring = () => {
  performanceInterval = setInterval(() => {
    const currentTime = performance.now();
    const deltaTime = currentTime - lastTime;
    
    fps.value = Math.round(frameCount * 1000 / deltaTime);
    renderTime.value = Math.round(deltaTime / frameCount * 100) / 100;
    
    frameCount = 0;
    lastTime = currentTime;
  }, 1000);
};

// 组件挂载时初始化
onMounted(() => {
  // 获取当前渲染质量设置
  if (props.sceneManager?.getRenderQuality) {
    const currentSettings = props.sceneManager.getRenderQuality();
    Object.assign(settings, currentSettings);
  }
  
  // 开始性能监控
  startPerformanceMonitoring();
});

// 组件卸载时清理
onBeforeUnmount(() => {
  if (performanceInterval) {
    clearInterval(performanceInterval);
  }
});

// 暴露给父组件
defineExpose({
  applyPreset,
  updateSettings: (newSettings) => Object.assign(settings, newSettings)
});
</script>

<style scoped>
.render-quality-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  background: rgba(30, 30, 30, 0.95);
  border: 1px solid #444;
  border-radius: 8px;
  color: white;
  font-size: 12px;
  z-index: 1000;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.panel-collapsed {
  width: 200px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  user-select: none;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.collapse-btn {
  transition: transform 0.3s ease;
}

.collapse-btn.collapsed {
  transform: rotate(-90deg);
}

.panel-content {
  padding: 16px;
  max-height: 70vh;
  overflow-y: auto;
}

.setting-group {
  margin-bottom: 20px;
}

.setting-group h4 {
  margin: 0 0 12px 0;
  color: #00bcd4;
  font-size: 13px;
  border-bottom: 1px solid #444;
  padding-bottom: 4px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  gap: 8px;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  flex: 1;
}

.setting-item input[type="range"] {
  flex: 1;
  margin: 0 8px;
}

.setting-item input[type="checkbox"] {
  margin: 0;
}

.value {
  min-width: 40px;
  text-align: right;
  color: #00bcd4;
  font-weight: bold;
}

.preset-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.preset-btn {
  padding: 6px 12px;
  background: #444;
  border: 1px solid #666;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 11px;
}

.preset-btn:hover {
  background: #555;
  border-color: #00bcd4;
}

.performance-info {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  font-family: monospace;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #888;
}
</style>
