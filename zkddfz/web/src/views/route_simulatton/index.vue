<template>
  <!-- class="relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 text-white overflow-hidden" -->
  <div
    class="relative from-slate-900 via-blue-900 to-slate-800 text-white overflow-hidden"
    :style="{ height: 'calc(104vh - 40px)' }"
  >
    <!-- 背景网格和装饰 -->
    <div class="absolute inset-0 pointer-events-none">
      <!-- 简洁网格背景 -->
      <div class="absolute inset-0 opacity-5">
        <div class="simple-grid-pattern w-full h-full"></div>
      </div>
      <!-- 顶部装饰线条 -->
      <!-- <div
        class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-400/30 to-transparent"
      ></div> -->
      <div
        class="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-400/30 to-transparent"
      ></div>
    </div>

    <!-- 顶部角形标题区域 -->
    <!-- <div class="absolute top-0 left-1/2 transform -translate-x-1/2 z-20 title"> -->
    <!-- <div class="angular-header-container relative w-[400px] h-[60px] overflow-hidden"> -->
    <!-- 角形背景 -->
    <!-- <div
          class="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-slate-700/90 to-slate-900/90 backdrop-blur-xl border border-cyan-400/40 shadow-[0_0_40px_rgba(6,182,212,0.2),inset_0_0_40px_rgba(6,182,212,0.05)]"
          style="clip-path: polygon(8% 0%, 92% 0%, 100% 50%, 92% 100%, 8% 100%, 0% 50%)"
        ></div> -->

    <!-- 主标题内容 -->
    <!-- <div class="relative z-30 h-full flex flex-col justify-center items-center gap-1">
          <div class="text-3xl font-bold text-white tracking-[0.15em] title-glow">智能调度仿真系统</div>
        </div> -->

    <!-- 边框扫描效果 -->
    <!-- <div class="absolute inset-0 z-20 pointer-events-none">
          <div
            class="border-scan-top absolute top-0 -left-full w-full h-0.5 bg-gradient-to-r from-transparent via-cyan-400 to-transparent animate-scan-horizontal"
          ></div>
          <div
            class="border-scan-bottom absolute bottom-0 -right-full w-full h-0.5 bg-gradient-to-r from-transparent via-cyan-400 to-transparent animate-scan-horizontal-reverse"
          ></div>
          <div
            class="border-scan-left absolute left-0 -top-full w-0.5 h-full bg-gradient-to-b from-transparent via-cyan-400 to-transparent animate-scan-vertical"
          ></div>
          <div
            class="border-scan-right absolute right-0 -bottom-full w-0.5 h-full bg-gradient-to-t from-transparent via-cyan-400 to-transparent animate-scan-vertical-reverse"
          ></div>
        </div> -->
    <!-- </div> -->
    <!-- </div> -->

    <!-- 3D可视化中央区域 -->
    <div class="absolute inset-0 flex items-center justify-center">
      <!-- <div class="absolute inset-0 flex items-center justify-center pt-20"> -->
      <div class="w-full h-full flex items-center justify-center relative">
        <!-- 中央3D视觉效果 -->
        <SceneManager
          ref="sceneManagerRef"
          :scene-config="sceneConfig"
          :models="models"
          height="calc(105vh - 48px)"
          @model-loaded="handleModelLoaded"
          @model-error="handleModelError"
          @scene-ready="handleSceneReady"
          @model-right-clicked="handleModelRightClicked"
          @map-loaded="handleMapLoaded"
          @map-load-progress="handleMapLoadProgress"
          @map-load-error="handleMapLoadError"
        >
          <!-- 科技风格覆盖层 -->
          <div class="scene-overlay">
            <div
              class="tech-loading absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center gap-4"
              v-if="loadingCount < totalModels"
            >
              <div class="loading-grid grid grid-cols-3 gap-1 w-12 h-12">
                <div
                  class="grid-item w-3 h-3 bg-cyan-400/60 animate-pulse"
                  v-for="i in 9"
                  :key="i"
                  :style="{ animationDelay: `${i * 0.1}s` }"
                ></div>
              </div>
              <span class="loading-text text-cyan-400 text-sm font-mono tracking-wider"
                >INITIALIZING... {{ loadingCount }}/{{ totalModels }}</span
              >
            </div>

            <!-- 3D模型信息卡片 -->
            <template v-if="showInfoCards">
              <ModelInfoCard3D
                v-for="model in models"
                :key="`card-3d-${model.id}`"
                :model="model"
                :scene="scene"
                :camera="sceneManagerRef?.camera"
                :visible="getCardVisibility(model)"
                :modelMesh="getModelMesh(model.id)"
              />
            </template>
          </div>
        </SceneManager>

        <!-- 渲染质量控制面板 -->
        <RenderQualityPanel
          :scene-manager="sceneManagerRef"
          v-if="sceneManagerRef"
        />
      </div>
    </div>

    <!-- Left Panel - 专业数据面板 -->
    <LeftPart
      :loadingStatus="loadingStatus"
      :firstPersonView="firstPersonView"
      :models="models"
      :sceneManagerRef="sceneManagerRef"
      :websocketSimulator="websocketSimulator"
      :pathExecutionSystem="pathExecutionSystem"
      @start-FirstPersonUpdate="startFirstPersonUpdate"
      @get-SelectedModelByIdForFirstPerson="getSelectedModelByIdForFirstPerson"
      @model-added="handleModelAdded"
      @config-exported="handleConfigExported"
      @config-imported="handleConfigImported"
      @scene-settings-changed="handleSceneSettingsChanged"
      @info-cards-toggled="handleInfoCardsToggled"
      @model-info-card-toggled="handleModelInfoCardToggled"
      @start-path-execution="startPathExecution"
      @stop-path-execution="stopPathExecution"
      @update-speed-multiplier="updateSpeedMultiplier"
      @map-loading="handleMapLoading"
      @map-loaded="handleMapLoaded"
      @obstacle-added="handleObstacleAdded"
      @obstacle-removed="handleObstacleRemoved"
      @set-camera-preset="setCameraPreset"
      @set-custom-camera-position="setCustomCameraPosition"
      @smooth-animation-changed="handleSmoothAnimationChanged"
    />

    <!-- Right Panel - 专业数据面板 -->
    <RightPart
      ref="rightPartRef"
      :loadingStatus="loadingStatus"
      :scene-config="sceneConfig"
      :totalModels="totalModels"
      :models="models"
      :sceneManagerRef="sceneManagerRef"
      :firstPersonView="firstPersonView"
      :websocketSimulator="websocketSimulator"
      :pathExecutionSystem="pathExecutionSystem"
      :availableRouteDeviceIds="availableRouteDeviceIds"
      :model="getSelectedModel()"
      :model-component="getSelectedModelComponent()"
      :map-interaction="mapInteraction"
      @status-change="handleRouteStatusChange"
      @position-update="handleRoutePositionUpdate"
      @model-selected="handleRightPanelModelSelected"
      @movement-started="handleRightPanelMovementStarted"
      @movement-stopped="handleRightPanelMovementStopped"
      @perform-energy-simulation="performEnergySimulation"
      @clear-start-point="clearStartPoint"
      @clear-end-point="clearEndPoint"
      @panel-collapsed="(collapsed) => rightPanelCollapsed = collapsed"
      @perception-simulation="handlePerceptionSimulation"
      @vehicle-type-changed="handleVehicleTypeChanged"
      @startFirstPersonUpdate="startFirstPersonUpdate"
      @getSelectedModelByIdForFirstPerson="getSelectedModelByIdForFirstPerson"
      @set-camera-preset="setCameraPreset"
      @set-custom-camera-position="setCustomCameraPosition"
      @smooth-animation-changed="handleSmoothAnimationChanged"
      @coordinate-request="handleCoordinateRequest"
      @schedule-submit="handleScheduleSubmit"
      @route-visibility-changed="handleRouteVisibilityChanged"
      @info-cards-toggled="handleInfoCardsToggled"
      @model-info-card-toggled="handleModelInfoCardToggled"
    />

    <!-- Alert Panel - 报警信息面板 -->
    <div class="absolute top-4 right-4 z-30 w-80">
      <div
        v-if="alerts.length > 0"
        class="alert-panel bg-slate-900/95 backdrop-blur-xl border border-red-500/40 rounded-lg shadow-2xl shadow-red-500/20 overflow-hidden"
      >
        <!-- Alert Header -->
        <div class="alert-header bg-gradient-to-r from-red-600/80 to-red-500/60 px-4 py-3 border-b border-red-500/30">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <div class="w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
              <h3 class="text-white font-bold text-sm">系统报警</h3>
              <span class="bg-red-500/30 text-red-200 text-xs px-2 py-1 rounded-full">{{ alerts.length }}</span>
            </div>
            <button
              @click="clearAllAlerts"
              class="text-red-200 hover:text-white transition-colors text-xs"
            >
              清除全部
            </button>
          </div>
        </div>

        <!-- Alert List -->
        <div class="alert-list max-h-96 overflow-y-auto">
          <div
            v-for="alert in alerts"
            :key="alert.id"
            class="alert-item border-b border-slate-700/50 last:border-b-0"
          >
            <div class="p-4 hover:bg-slate-800/50 transition-colors">
              <div class="flex items-start gap-3">
                <!-- Alert Icon -->
                <div class="flex-shrink-0 mt-0.5">
                  <div
                    class="w-6 h-6 rounded-full flex items-center justify-center"
                    :class="{
                      'bg-red-500/20 text-red-400': alert.type === 'battery',
                      'bg-orange-500/20 text-orange-400': alert.type === 'deviation',
                      'bg-yellow-500/20 text-yellow-400': alert.type === 'warning'
                    }"
                  >
                    <!-- Battery Low Icon -->
                    <svg v-if="alert.type === 'battery'" class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M15.67 4H14V2h-4v2H8.33C7.6 4 7 4.6 7 5.33v15.33C7 21.4 7.6 22 8.33 22h7.33c.74 0 1.34-.6 1.34-1.33V5.33C17 4.6 16.4 4 15.67 4z"/>
                    </svg>
                    <!-- Deviation Icon -->
                    <svg v-else-if="alert.type === 'deviation'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                    <!-- Warning Icon -->
                    <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                </div>

                <!-- Alert Content -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between mb-1">
                    <h4 class="text-white font-medium text-sm">{{ alert.title }}</h4>
                    <button
                      @click="dismissAlert(alert.id)"
                      class="text-slate-400 hover:text-white transition-colors"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                      </svg>
                    </button>
                  </div>
                  <p class="text-slate-300 text-xs mb-2">{{ alert.message }}</p>
                  <div class="flex items-center justify-between">
                    <span class="text-slate-500 text-xs">{{ alert.deviceName }}</span>
                    <span class="text-slate-500 text-xs">{{ formatTime(alert.timestamp) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 地图右键菜单（优化版） -->
    <Transition
      enter-active-class="transition-all duration-150 ease-out"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition-all duration-100 ease-in"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div
        v-if="mapInteraction.showContextMenu"
        class="context-menu fixed z-50 bg-slate-800/95 backdrop-blur-sm border border-cyan-500/30 rounded-lg shadow-xl shadow-cyan-500/20 min-w-48"
        :style="{
          left: mapInteraction.contextMenuPosition.x + 'px',
          top: mapInteraction.contextMenuPosition.y + 'px',
          transform: 'translate(-50%, -10px)', // 避免菜单超出屏幕
        }"
        @click.stop
      >
        <div class="p-2">
          <div v-if="mapInteraction.clickedPoint" class="text-xs text-slate-400 mb-2 px-2">
            坐标: ({{ mapInteraction.clickedPoint.x }}, {{ mapInteraction.clickedPoint.z }})
          </div>
          <div v-else class="text-xs text-slate-500 mb-2 px-2">计算坐标中...</div>
          <button
            @click="setStartPoint"
            :disabled="!mapInteraction.clickedPoint"
            class="w-full text-left px-3 py-2 text-sm text-white hover:bg-cyan-500/20 rounded transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            🎯 设置为起点
          </button>
          <button
            @click="setEndPoint"
            :disabled="!mapInteraction.clickedPoint"
            class="w-full text-left px-3 py-2 text-sm text-white hover:bg-cyan-500/20 rounded transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            🏁 设置为终点
          </button>
          <!-- 调度坐标设置选项 -->
          <div v-if="scheduleRequest.currentProcessType && mapInteraction.clickedPoint" class="border-t border-slate-600/50 mt-1 pt-2">
            <div class="text-xs text-slate-400 mb-2 px-2">
              {{ getProcessTypeName(scheduleRequest.currentProcessType) }} - 坐标设置
            </div>
            <button
              v-for="field in getAvailableCoordinateFields(scheduleRequest.currentProcessType)"
              :key="field.key"
              @click="setScheduleCoordinate(field.key)"
              class="w-full text-left px-3 py-2 text-sm text-purple-300 hover:bg-purple-500/20 rounded transition-colors duration-150 mb-1"
            >
              📍 设置为{{ field.label }}
            </button>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 模型右键菜单 -->
    <Transition
      enter-active-class="transition-all duration-150 ease-out"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition-all duration-100 ease-in"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div
        v-if="contextMenu.visible"
        class="context-menu fixed z-50 bg-slate-800/95 backdrop-blur-sm border border-cyan-500/30 rounded-lg shadow-xl shadow-cyan-500/20 min-w-48"
        :style="{
          left: contextMenu.x + 'px',
          top: contextMenu.y + 'px',
          transform: 'translate(-50%, -10px)'
        }"
        @click.stop
      >
        <div class="p-2">
          <div class="text-xs text-slate-400 mb-2 px-2">
            模型: {{ getModelName(contextMenu?.targetModelId) }}
          </div>
          <button
            @click="enterFirstPersonView"
            class="w-full text-left px-3 py-2 text-sm text-white hover:bg-cyan-500/20 rounded transition-colors duration-150 flex items-center gap-2"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
            </svg>
            {{ firstPersonView?.targetModelId === contextMenu?.targetModelId ? '退出驾驶员视角' : '进入驾驶员视角' }}
          </button>
          <button
            @click="selectModelForMovement"
            class="w-full text-left px-3 py-2 text-sm text-white hover:bg-cyan-500/20 rounded transition-colors duration-150 flex items-center gap-2"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11"/>
            </svg>
            选择为移动目标
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { computed, nextTick, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
import SceneManager from './components/SceneManager.vue';
import RenderQualityPanel from './components/RenderQualityPanel.vue';
import LeftPart from './left_part/index.vue';
import RightPart from './right_part/index.vue';
import ModelInfoCard from './components/ModelInfoCard.vue';
import ModelInfoCard3D from './components/ModelInfoCard3D.vue';
import { RouteSimulator } from './utils/RouteSimulator.js';
import { RouteRenderer } from './utils/RouteRenderer.js';

// 场景配置
const sceneConfig = reactive({
  background: 0x86c3f3,
  lights: [
    { type: 'ambient', color: 0xf2f5cd, intensity: 4 },
    {
      type: 'directional',
      color: 0xffffff,
      intensity: 2,
      position: { x: 500, y: 800, z: -500 },
      castShadow: true,
      shadow: {
        mapSize: { width: 4096, height: 4096 },
        camera: {
          left: 0,
          right: 2000,
          top: 0,
          bottom: -2000,
          near: 0.5,
          far: 1000,
        },
        bias: -0.0001,
        normalBias: 0.02,
      },
    },
  ],
  camera: {
    position: { x: 680, y: 100, z: -430 },
    lookAt: { x: 45, y: 45, z: 45 },
  },
  ground: {
    size: 1,
    color: 0x999999,
    receiveShadow: true,
  },
  skybox: {
    enabled: true,
    type: 'sphere', // 'cube', 'sphere', 'gradient'
    // 立方体天空盒贴图路径（6个面）
    textures: {
      px: '/img/sky.jpg', // 右
      nx: '/img/sky.jpg', // 左
      py: '/img/sky.jpg', // 上
      ny: '/img/sky.jpg', // 下
      pz: '/img/sky.jpg', // 前
      nz: '/img/sky.jpg', // 后
    },
    // 球形天空盒贴图路径
    texture: '/img/sky4.jpg',
  },
  mapModel: {
    enabled: true,
    path: '/models/map12.glb', // 地图模型路径
    // position: {x: 0, y: 0, z: 0}, // 地图位置
    rotation: { x: 0, y: 0, z: 0 }, // 地图旋转（度）
    scale: 1, // 地图缩放
    receiveShadow: true, // 接收阴影
    castShadow: true,// 投射阴影
  },
});

// 模型配置（使用地面坐标系：Y=0表示地面）
const models = ref([])
// const models = ref([
//   {
//     id: 'car1',
//     name: '1500T斗轮挖掘机',
//     path: '/models/1500T斗轮挖掘机.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: -80, y: 0, z: -60 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 45, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: -80, y: 0, z: -60 },
//     currentRotation: { x: 0, y: 45, z: 0 },
//     scale: 0.01,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x02536c,
//       roughness: 0.3, // 粗糙度 (0.0-1.0, 0为完全光滑，1为完全粗糙)
//       metalness: 0.8, // 金属度 (0.0-1.0, 0为非金属，1为完全金属)
//     },
//     battery: 85, // 电量85%
//     sensors: [
//       {
//         id: 'laser_1',
//         name: '前置激光雷达',
//         relativePosition: { x: 2, y: 1.5, z: 0 },
//         pitchRange: { min: -15, max: 15 },
//         yawRange: { min: -90, max: 90 },
//         laserDensity: 10,
//         enabled: true,
//       },
//     ],
//   },
//   {
//     id: 'car3',
//     name: '3500T斗轮挖掘机',
//     path: '/models/3500T斗轮挖掘机.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: 120, y: 0, z: -90 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 180, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: 680, y: 0.01, z: -445 },
//     currentRotation: { x: 0, y: 90, z: 0 },
//     scale: 0.01,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x02536c,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 桥式转载机表面较粗糙
//       metalness: 0.9, // 金属度 (0.0-1.0) - 中等金属度
//     },
//     battery: 55, // 电量25% - 低电量状态
//     sensors: [
//       {
//         id: 'laser_2',
//         name: '360度激光雷达',
//         relativePosition: { x: 0, y: 2, z: 0 },
//         pitchRange: { min: -30, max: 30 },
//         yawRange: { min: -180, max: 180 },
//         laserDensity: 15,
//         enabled: true,
//       },
//       {
//         id: 'laser_3',
//         name: '后置激光雷达',
//         relativePosition: { x: -2, y: 1, z: 0 },
//         pitchRange: { min: -10, max: 10 },
//         yawRange: { min: -45, max: 45 },
//         laserDensity: 8,
//         enabled: false,
//       },
//     ],
//   },
//   {
//     id: 'car5',
//     name: '扇形布料机',
//     path: '/models/扇形布料机ok.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: -150, y: 0, z: 80 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 270, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: 675, y: 0.01, z: -620 },
//     currentRotation: { x: 0, y: 270, z: 0 },
//     scale: 0.01,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x0f6c02,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 桥式转载机表面较粗糙
//       metalness: 0.9, // 金属度 (0.0-1.0) - 中等金属度
//     },
//   },
//   {
//     id: 'car23',
//     name: '扇形布料机',
//     path: '/models/扇形布料机ok.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: -150, y: 0, z: 80 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 270, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: 675, y: 0.01, z: -620 },
//     currentRotation: { x: 0, y: 270, z: 0 },
//     scale: 0.01,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x0f6c02,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 桥式转载机表面较粗糙
//       metalness: 0.9, // 金属度 (0.0-1.0) - 中等金属度
//     },
//   },
//   {
//     id: 'car6',
//     name: '中继转载机',
//     path: '/models/中继转载机-B-ok.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: 0, y: 0, z: 0 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 90, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: 675, y: 0.01, z: -520 },
//     currentRotation: { x: 0, y: 180, z: 0 },
//     scale: 0.01,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x3a6c02,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 桥式转载机表面较粗糙
//       metalness: 0.9, // 金属度 (0.0-1.0) - 中等金属度
//     },
//   },
//   {
//     id: 'car22',
//     name: '中继转载机',
//     path: '/models/中继转载机-B-ok.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: 0, y: 0, z: 0 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 90, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: 675, y: 0.01, z: -520 },
//     currentRotation: { x: 0, y: 180, z: 0 },
//     scale: 0.01,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x3a6c02,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 桥式转载机表面较粗糙
//       metalness: 0.9, // 金属度 (0.0-1.0) - 中等金属度
//     },
//   },
//   {
//     id: 'car7',
//     name: '大型布料机',
//     path: '/models/大型布料机ok-6.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: 0, y: 0, z: 0 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 0, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: 675, y: 0.01, z: -496 },
//     currentRotation: { x: 0, y: -180, z: 0 },
//     scale: 0.0088,
//     receiveShadow: true,
//     castShadow: true,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x02536c,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//       metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//     },
//     battery: 95, // 电量95%
//     sensors: [], // 暂无传感器
//   },
//   {
//     id: 'car8',
//     name: '桥式转载机',
//     path: '/models/桥式转载机ok.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: 180, y: 0, z: 50 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 135, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: 680, y: 0, z: -520 },
//     currentRotation: { x: 0, y: 0, z: 0 },
//     scale: 0.005,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x6c0202,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//       metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//     },
//   },
//   {
//     id: 'car21',
//     name: '桥式转载机',
//     path: '/models/桥式转载机ok.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: 180, y: 0, z: 50 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 135, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: 180, y: 0, z: 50 },
//     currentRotation: { x: 0, y: 135, z: 0 },
//     scale: 0.005,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x6c0202,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//       metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//     },
//   },
//   {
//     id: 'car9',
//     name: '移动供电车',
//     path: '/models/移动供电车ok.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: -100, y: 0, z: -30 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 60, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: -100, y: 0, z: -30 },
//     currentRotation: { x: 0, y: 60, z: 0 },
//     scale: 0.01,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x02536c,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//       metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//     },
//   },
//   {
//     id: 'car10',
//     name: '移动分料漏斗',
//     path: '/models/移动分料漏斗ok.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: 30, y: 0, z: -160 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 225, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: 680, y: 0, z: -430 },
//     currentRotation: { x: 0, y: 225, z: 0 },
//     scale: 0.01,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x026c20,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//       metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//     },
//   },
//   {
//     id: 'car11',
//     name: '移动受料漏斗',
//     path: '/models/移动受料漏斗ok.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: -70, y: 0, z: 110 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 150, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: -70, y: 0, z: 110 },
//     currentRotation: { x: 0, y: 150, z: 0 },
//     scale: 0.01,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x026c20,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//       metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//     },
//   },
//   {
//     id: 'car12',
//     name: '移动转载机',
//     path: '/models/移动转载机ok.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 300, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: 675, y: 0, z: -596 },
//     currentRotation: { x: 0, y: 90, z: 0 },
//     scale: 0.01,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x02536c,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//       metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//     },
//   },
//   {
//     id: 'car13',
//     name: '移动转载机',
//     path: '/models/移动转载机ok.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 300, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: 674, y: 0, z: -450 },
//     currentRotation: { x: 0, y: 280, z: 0 },
//     scale: 0.01,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x02536c,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//       metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//     },
//   },
//   // {
//   //   id: 'car14',
//   //   name: '移动转载机',
//   //   path: '/models/移动转载机ok.glb',
//   //   // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//   //   initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//   //   initialRotation: { x: 0, y: 300, z: 0 },
//   //   // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//   //   currentPosition: { x: 687, y: 0, z: -490 },
//   //   currentRotation: { x: 0, y: 270, z: 0 },
//   //   scale: 0.01,
//   //   defaultAnimation: 0,
//   //   movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//   //   autoGroundPosition: true, // 自动地面定位
//   //   material: {
//   //     color: 0x02536c,
//   //     roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//   //     metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//   //   },
//   // },
//   // {
//   //   id: 'car15',
//   //   name: '移动转载机',
//   //   path: '/models/移动转载机ok.glb',
//   //   // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//   //   initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//   //   initialRotation: { x: 0, y: 300, z: 0 },
//   //   // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//   //   currentPosition: { x: 690, y: 0, z: -490 },
//   //   currentRotation: { x: 0, y: 270, z: 0 },
//   //   scale: 0.01,
//   //   defaultAnimation: 0,
//   //   movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//   //   autoGroundPosition: true, // 自动地面定位
//   //   material: {
//   //     color: 0x02536c,
//   //     roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//   //     metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//   //   },
//   // },
//   // {
//   //   id: 'car16',
//   //   name: '移动转载机',
//   //   path: '/models/移动转载机ok.glb',
//   //   // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//   //   initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//   //   initialRotation: { x: 0, y: 300, z: 0 },
//   //   // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//   //   currentPosition: { x: 670, y: 0, z: -490 },
//   //   currentRotation: { x: 0, y: 268, z: 0 },
//   //   scale: 0.01,
//   //   defaultAnimation: 0,
//   //   movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//   //   autoGroundPosition: true, // 自动地面定位
//   //   material: {
//   //     color: 0x02536c,
//   //     roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//   //     metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//   //   },
//   // },
//   // {
//   //   id: 'car17',
//   //   name: '移动转载机',
//   //   path: '/models/移动转载机ok.glb',
//   //   // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//   //   initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//   //   initialRotation: { x: 0, y: 300, z: 0 },
//   //   // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//   //   currentPosition: { x: 651, y: 0, z: -512 },
//   //   currentRotation: { x: 0, y: 0, z: 0 },
//   //   scale: 0.01,
//   //   defaultAnimation: 0,
//   //   movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//   //   autoGroundPosition: true, // 自动地面定位
//   //   material: {
//   //     color: 0x02536c,
//   //     roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//   //     metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//   //   },
//   // },
//   // {
//   //   id: 'car18',
//   //   name: '移动转载机',
//   //   path: '/models/移动转载机ok.glb',
//   //   // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//   //   initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//   //   initialRotation: { x: 0, y: 300, z: 0 },
//   //   // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//   //   currentPosition: { x: 710, y: 0, z: -512 }, // 地面坐标系：Y=0表示放在地面上
//   //   currentRotation: { x: 0, y: 180, z: 0 },
//   //   scale: 0.01,
//   //   defaultAnimation: 0,
//   //   movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//   //   autoGroundPosition: true, // 自动地面定位
//   //   material: {
//   //     color: 0x02536c,
//   //     roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//   //     metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//   //   },
//   // },
//   // {
//   //   id: 'car19',
//   //   name: '移动转载机',
//   //   path: '/models/移动转载机ok.glb',
//   //   // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//   //   initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//   //   initialRotation: { x: 0, y: 300, z: 0 },
//   //   // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//   //   currentPosition: { x: 752, y: 0, z: -512 },
//   //   currentRotation: { x: 0, y: 180, z: 0 },
//   //   scale: 0.01,
//   //   defaultAnimation: 0,
//   //   movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//   //   autoGroundPosition: true, // 自动地面定位
//   //   material: {
//   //     color: 0x02536c,
//   //     roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//   //     metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//   //   },
//   // },
//   // {
//   //   id: 'car20',
//   //   name: '移动转载机',
//   //   path: '/models/移动转载机ok.glb',
//   //   // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//   //   initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//   //   initialRotation: { x: 0, y: 300, z: 0 },
//   //   // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//   //   currentPosition: { x: 609, y: 0, z: -512 },
//   //   currentRotation: { x: 0, y: 0, z: 0 },
//   //   scale: 0.01,
//   //   defaultAnimation: 0,
//   //   movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//   //   autoGroundPosition: true, // 自动地面定位
//   //   material: {
//   //     color: 0x02536c,
//   //     roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//   //     metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//   //   },
//   // },
//   // {
//   //   id: 'car23',
//   //   name: '移动转载机',
//   //   path: '/models/移动转载机ok.glb',
//   //   // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//   //   initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//   //   initialRotation: { x: 0, y: 300, z: 0 },
//   //   // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//   //   currentPosition: { x: 577, y: 0, z: -512 },
//   //   currentRotation: { x: 0, y: 0, z: 0 },
//   //   scale: 0.01,
//   //   defaultAnimation: 0,
//   //   movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//   //   autoGroundPosition: true, // 自动地面定位
//   //   material: {
//   //     color: 0x02536c,
//   //     roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//   //     metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//   //   },
//   // },
//   // {
//   //   id: 'car24',
//   //   name: '移动转载机',
//   //   path: '/models/移动转载机ok.glb',
//   //   // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//   //   initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//   //   initialRotation: { x: 0, y: 300, z: 0 },
//   //   // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//   //   currentPosition: { x: 577, y: 0, z: -512 },
//   //   currentRotation: { x: 0, y: 0, z: 0 },
//   //   scale: 0.01,
//   //   defaultAnimation: 0,
//   //   movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//   //   autoGroundPosition: true, // 自动地面定位
//   //   material: {
//   //     color: 0x02536c,
//   //     roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//   //     metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//   //   },
//   // },
//   // {
//   //   id: 'car25',
//   //   name: '移动转载机',
//   //   path: '/models/移动转载机ok.glb',
//   //   // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//   //   initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//   //   initialRotation: { x: 0, y: 300, z: 0 },
//   //   // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//   //   currentPosition: { x: 577, y: 0, z: -512 },
//   //   currentRotation: { x: 0, y: 0, z: 0 },
//   //   scale: 0.01,
//   //   defaultAnimation: 0,
//   //   movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//   //   autoGroundPosition: true, // 自动地面定位
//   //   material: {
//   //     color: 0x02536c,
//   //     roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//   //     metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//   //   },
//   // },
//   // {
//   //   id: 'car26',
//   //   name: '移动转载机',
//   //   path: '/models/移动转载机ok.glb',
//   //   // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//   //   initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//   //   initialRotation: { x: 0, y: 300, z: 0 },
//   //   // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//   //   currentPosition: { x: 577, y: 0, z: -512 },
//   //   currentRotation: { x: 0, y: 0, z: 0 },
//   //   scale: 0.01,
//   //   defaultAnimation: 0,
//   //   movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//   //   autoGroundPosition: true, // 自动地面定位
//   //   material: {
//   //     color: 0x02536c,
//   //     roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//   //     metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//   //   },
//   // },
//   // {
//   //   id: 'car27',
//   //   name: '移动转载机',
//   //   path: '/models/移动转载机ok.glb',
//   //   // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//   //   initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//   //   initialRotation: { x: 0, y: 300, z: 0 },
//   //   // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//   //   currentPosition: { x: 577, y: 0, z: -512 },
//   //   currentRotation: { x: 0, y: 0, z: 0 },
//   //   scale: 0.01,
//   //   defaultAnimation: 0,
//   //   movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//   //   autoGroundPosition: true, // 自动地面定位
//   //   material: {
//   //     color: 0x02536c,
//   //     roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//   //     metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//   //   },
//   // },
//   {
//     id: 'worker1',
//     name: '移动转载机',
//     path: '/models/person2.glb',
//     // 初始坐标和角度（模型的基准位置，加载时的默认位置）
//     initialPosition: { x: 140, y: 0, z: 20 }, // 地面坐标系：Y=0表示放在地面上
//     initialRotation: { x: 0, y: 300, z: 0 },
//     // 当前坐标和角度（实时位置，会被JSON数据或移动操作更新）
//     currentPosition: { x: 680, y: 0, z: -430 },
//     currentRotation: { x: 0, y: 90, z: 0 },
//     scale: 4,
//     defaultAnimation: 0,
//     movementSpeed: 30.0, // 汽车移动速度 (单位/秒)，适合大地图
//     autoGroundPosition: true, // 自动地面定位
//     material: {
//       color: 0x02536c,
//       roughness: 0.2, // 粗糙度 (0.0-1.0) - 布料机表面相对光滑
//       metalness: 0.9, // 金属度 (0.0-1.0) - 高金属度
//     },
//   },
// ]);

// 移动控制状态（使用地面坐标系）
const movementControls = ref({
  selectedModelId: null, // 初始为空，会自动选择第一个可用模型
  targetPosition: { x: 0, y: 0, z: 0 }, // 地面坐标系：Y=0表示地面
  movementSpeed: 2.0,
  isMoving: false,
  keyboardControlEnabled: true, // 键盘控制开关
});

// 报警系统
const alerts = ref([]);
let alertIdCounter = 0;

// 报警阈值配置
const alertThresholds = {
  lowBattery: 30, // 低电量阈值
  deviationDistance: 5, // 走行偏移距离阈值 (米)
};

// 设备预期路径存储 (用于偏移检测)
const expectedPaths = ref(new Map());

// 键盘控制
const keyboardControls = ref({
  stepSize: 1.0, // 每次移动的步长
  isActive: false,
});

// 信息卡片显示控制
const showInfoCards = ref(false);

// 模型屏幕位置缓存
const modelScreenPositions = ref(new Map());

// 自由视角控制
const freeCameraControls = ref({
  enabled: false,
  movementSpeed: 20.0, // 相机移动速度，默认20
  conflictWarning: false, // 冲突警告
});

// 控制台展开/关闭状态
const controlPanelVisible = ref(true);

// 面板展开/关闭状态
const panelStates = ref({
  modelList: false, // 模型列表
  modelCoordinates: false, // 模型坐标管理
  movement: false, // 移动控制
  freeCamera: false, // 自由视角控制
  firstPersonView: false, // 第一视角控制
  routeSimulation: false, // 路线模拟控制
  sceneConfig: false, // 场景配置
});

// 第一视角控制
const firstPersonView = ref({
  enabled: false, // 是否启用第一视角
  targetModelId: null, // 目标模型ID
  cameraOffset: {
    // 相机在模型坐标系中的相对位置 - 默认驾驶员视角
    x: 0,
    y: 4, // 驾驶室高度4米
    z: 2, // 驾驶室前方2米
  },
  lookAtOffset: {
    // 相机朝向目标在模型坐标系中的相对位置 - 默认驾驶员视角
    x: 0,
    y: 3, // 略微向下看
    z: -20, // 朝向前方远处20米
  },
  followRotation: true, // 是否跟随模型旋转
  followMovement: true, // 是否跟随模型移动
  smoothFollow: false, // 禁用平滑跟随，使用即时跟随
  updateInterval: null, // 更新间隔ID
  originalCameraPosition: new THREE.Vector3(), // 保存原始相机位置
  originalCameraRotation: new THREE.Euler(), // 保存原始相机旋转
  originalControlsTarget: new THREE.Vector3(), // 保存原始控制器目标

  // 预设相机位置
  presets: {
    'driver': { // 驾驶员视角 - 模拟驾驶室内视角
      camera: { x: 0, y: 2111, z: 2111 }, // 默认使用驾驶员视角
      lookAt: { x: 0, y: 1111, z: -20 }
    },
    'overhead': { // 俯视视角 - 从上方俯视
      camera: { x: 0, y: 12111, z: 2111 }, // 默认使用驾驶员视角
      lookAt: { x: 0, y: 1111, z: -20 }
    },
    'follow': { // 跟随视角 - 第三人称跟随
      camera: { x: 0, y: 12111, z: 2111 }, // 默认使用驾驶员视角
      lookAt: { x: 0, y: 1111, z: -20 }
    },
    'side': { // 侧视视角 - 侧面观察
      camera: { x: 0, y: 12111, z: 2111 }, // 默认使用驾驶员视角
      lookAt: { x: 0, y: 1111, z: -20 }
    },
    'custom': { // 自定义视角
      camera: { x: 0, y: 2111, z: 2111 }, // 默认使用驾驶员视角
      lookAt: { x: 0, y: 1111, z: -20 }
    }
  },
  currentPreset: 'driver' // 当前选中的预设
});

// 右键菜单控制
const contextMenu = ref({
  visible: false, // 菜单是否可见
  x: 0, // 菜单X位置
  y: 0, // 菜单Y位置
  targetModelId: null, // 目标模型ID
});

// 场景管理器引用
const sceneManagerRef = ref(null);

// 右侧面板引用
const rightPartRef = ref(null);

// 加载状态
const loadingStatus = ref({});
const loadingCount = ref(0);
const totalModels = ref(models.value.length);

// 场景引用
const scene = ref(null);

// 已加载模型的引用
const loadedModels = ref({});

// 处理模型加载完成事件
const handleModelLoaded = data => {
  loadingStatus.value[data.id] = 'loaded';
  loadingCount.value++;
  console.log(`模型 ${data.id} 加载完成`);
  // 获取模型的边界框信息用于调试
  if (sceneManagerRef.value) {
    setTimeout(() => {
      const boundingBox = sceneManagerRef.value.getModelBoundingBox(data.id);
      if (boundingBox) {
        console.log(`模型 ${data.id} 边界框:`, boundingBox);
      }
    }, 100);
  }
};

// 处理模型加载错误事件
const handleModelError = data => {
  loadingStatus.value[data.id] = 'error';
  loadingCount.value++;
  console.error(`模型 ${data.id} 加载失败:`, data.error);
  // 如果模型加载失败，创建一个简单的几何体替代
  createFallbackModel(data.id);
};

// 创建替代模型
const createFallbackModel = modelId => {
  const modelConfig = models.value.find(m => m.id === modelId);
  if (!modelConfig || !scene.value) return;

  // 创建一个简单的几何体
  let geometry;
  if (modelId === 'model1') {
    geometry = new THREE.BoxGeometry(1, 1, 1);
  } else {
    geometry = new THREE.SphereGeometry(0.7, 32, 32);
  }

  const material = new THREE.MeshStandardMaterial({
    color: modelConfig.material?.color || 0xff0000,
    metalness: modelConfig.material?.metalness || 0.5,
    roughness: modelConfig.material?.roughness || 0.5,
  });

  const mesh = new THREE.Mesh(geometry, material);

  // 设置位置、旋转和缩放
  if (modelConfig.position) {
    mesh.position.set(modelConfig.position.x || 0, modelConfig.position.y || 0, modelConfig.position.z || 0);
  }

  if (modelConfig.rotation) {
    mesh.rotation.set(
      THREE.MathUtils.degToRad(modelConfig.rotation.x || 0),
      THREE.MathUtils.degToRad(modelConfig.rotation.y || 0),
      THREE.MathUtils.degToRad(modelConfig.rotation.z || 0),
    );
  }

  if (modelConfig.scale) {
    const scale = typeof modelConfig.scale === 'number' ? modelConfig.scale : 1;
    mesh.scale.set(scale, scale, scale);
  }

  // 添加到场景
  scene.value.add(mesh);

  // 保存引用
  loadedModels.value[modelId] = mesh;
};

// 处理场景准备完成事件
const handleSceneReady = data => {
  scene.value = data.scene;
  console.log('Scene is ready, scene object:', !!scene.value);
  console.log('Current showInfoCards state:', showInfoCards.value);
};

// 处理地图加载完成事件
const handleMapLoaded = async (data) => {
  // 如果是从SceneManager来的事件（旧格式）
  if (data.id && data.model && data.config) {
    console.log('🗺️ 地图加载完成!');
    console.log('- 地图ID:', data.id);
    console.log('- 地图模型:', data.model);
    console.log('- 地图配置:', data.config);

    if (data.stats) {
      console.log('📊 地图统计信息:');
      console.log(`- 网格数量: ${data.stats.meshCount}`);
      console.log(`- 顶点数量: ${data.stats.totalVertices}`);
      console.log(`- 面数量: ${data.stats.totalFaces}`);
      console.log('- 边界框尺寸:', data.stats.boundingBox.size);
      console.log('- 边界框中心:', data.stats.boundingBox.center);

      // 检查地图是否可能被裁剪
      const size = data.stats.boundingBox.size;
      if (size.x > 1000 || size.z > 1000) {
        console.warn('⚠️ 地图尺寸很大，请确认相机位置和缩放设置');
      }

      // 检查地图是否在视野范围内
      const center = data.stats.boundingBox.center;
      if (Math.abs(center.x) > 500 || Math.abs(center.z) > 500) {
        console.warn('⚠️ 地图中心位置较远，可能需要调整相机位置');
      }
    }

    // 显示成功提示
    console.log('✅ 地图已成功加载到场景中');
    return;
  }

  // 如果是从左侧面板来的事件（新格式）
  if (data.name && data.path) {
    console.log('🗺️ 接收到地图配置:', data.name);
    console.log('📋 地图详细配置:', data);

    try {
      if (sceneManagerRef.value && data.path) {
        console.log('🔄 开始加载地图模型到3D场景...');

        // 先清除当前地图
        clearCurrentMap();

        // 加载新的地图模型
        await loadMapModel(data);
        currentMap.value = data;

        console.log('✅ 地图模型已成功加载到场景:', data.name);

        // 可选：显示成功提示
        // alert(`地图 "${data.name}" 加载成功！`);
      } else {
        console.error('场景管理器未初始化或地图路径无效');
      }
    } catch (error) {
      console.error('地图模型加载失败:', error);
      alert('地图加载失败：' + error.message);
    }
  }
};

// 处理地图加载进度事件
const handleMapLoadProgress = data => {
  console.log(`📥 地图加载进度: ${data.percent}% (${data.loadedMB}MB / ${data.totalMB}MB)`);
  // 如果文件很大，给出提示
  if (data.totalMB > 50) {
    console.log('📦 地图文件较大，请耐心等待...');
  }
};

// 处理地图加载错误事件
const handleMapLoadError = data => {
  console.error('地图加载失败!');
  console.error('- 错误信息:', data.error);
  console.error('- 文件路径:', data.path);
  console.error('- 错误类型:', data.type);
  console.error('- 错误消息:', data.message);

  // 提供解决建议
  let suggestion = '';
  if (data.message && data.message.includes('404')) {
    suggestion = '请检查地图文件是否存在于指定路径';
  } else if (data.message && data.message.includes('CORS')) {
    suggestion = '请确保服务器允许跨域访问GLB文件';
  } else if (data.message && data.message.includes('network')) {
    suggestion = '请检查网络连接是否正常';
  } else {
    suggestion = '请检查GLB文件是否完整且格式正确';
  }

  console.error('💡 建议:', suggestion);

  // 显示用户友好的错误提示
  alert(`地图加载失败!\n\n错误: ${data.message || data.error}\n建议: ${suggestion}\n\n请检查控制台获取详细信息。`);
};

// 报警系统函数
const addAlert = (type, title, message, deviceId, deviceName) => {
  const alert = {
    id: ++alertIdCounter,
    type,
    title,
    message,
    deviceId,
    deviceName,
    timestamp: Date.now(),
  };

  // 避免重复报警 (同一设备同一类型的报警在5分钟内只显示一次)
  const existingAlert = alerts.value.find(
    a => a.deviceId === deviceId && a.type === type && (Date.now() - a.timestamp) < 300000
  );

  if (!existingAlert) {
    alerts.value.unshift(alert);
    // 限制报警数量，最多保留20条
    if (alerts.value.length > 20) {
      alerts.value = alerts.value.slice(0, 20);
    }
  }
};

const dismissAlert = (alertId) => {
  const index = alerts.value.findIndex(alert => alert.id === alertId);
  if (index !== -1) {
    alerts.value.splice(index, 1);
  }
};

const clearAllAlerts = () => {
  alerts.value = [];
};

const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 检查电量报警
const checkBatteryAlerts = () => {
  models.value.forEach(model => {
    const battery = model.battery || 100;
    if (battery < alertThresholds.lowBattery) {
      // addAlert(
      //   'battery',
      //   '电量过低警告',
      //   `设备电量仅剩 ${battery}%，请及时充电`,
      //   model.id,
      //   model.name
      // );
      // addAlert(
      //     'deviation',
      //     '走行偏移警告',
      //     `设备电量走行偏移，请及时纠正`,
      //     model.id,
      //     model.name
      // );
    }
  });
};

// 检查走行偏移报警
const checkDeviationAlerts = (modelId, currentPosition, expectedPosition) => {
  if (!expectedPosition) return;

  const distance = Math.sqrt(
    Math.pow(currentPosition.x - expectedPosition.x, 2) +
    Math.pow(currentPosition.z - expectedPosition.z, 2)
  );

  if (distance > alertThresholds.deviationDistance) {
    const model = models.value.find(m => m.id === modelId);
    if (model) {
      addAlert(
        'deviation',
        '走行偏移警告',
        `设备偏离预定路径 ${distance.toFixed(1)} 米，请检查导航系统`,
        modelId,
        model.name
      );
    }
  }
};

// 移动模型（即时移动）
const moveModel = (modelId, position) => {
  if (sceneManagerRef.value) {
    sceneManagerRef.value.updateModelPosition(modelId, position);
  }
};

// 平滑移动模型到目标位置（地面坐标系）
const moveModelToPosition = async (modelId, position, speed = null, immediate = false) => {
  if (sceneManagerRef.value) {
    const result = await sceneManagerRef.value.moveModelToPosition(modelId, position, speed, immediate);
    // 更新模型配置中的currentPosition
    const model = models.value.find(m => m.id === modelId);
    if (model && model.currentPosition) {
      model.currentPosition.x = position.x;
      model.currentPosition.y = position.y;
      model.currentPosition.z = position.z;
    }
    return result;
  }
};

// 立即设置模型位置（地面坐标系）
const setModelPositionImmediate = (modelId, position) => {
  if (sceneManagerRef.value) {
    sceneManagerRef.value.setModelPositionImmediate(modelId, position);
    // 更新模型配置中的currentPosition
    const model = models.value.find(m => m.id === modelId);
    if (model && model.currentPosition) {
      model.currentPosition.x = position.x;
      model.currentPosition.y = position.y;
      model.currentPosition.z = position.z;
    }
  }
};

// 设置模型移动速度
const setModelMovementSpeed = (modelId, speed) => {
  if (sceneManagerRef.value) {
    sceneManagerRef.value.setModelMovementSpeed(modelId, speed);
  }
  // 同时更新模型配置
  const model = models.value.find(m => m.id === modelId);
  if (model) {
    model.movementSpeed = speed;
  }
};

// 获取模型当前位置
const getModelPosition = modelId => {
  if (sceneManagerRef.value) {
    return sceneManagerRef.value.getModelPosition(modelId);
  }
  return null;
};

// 停止模型移动
const stopModelMovement = modelId => {
  if (sceneManagerRef.value) {
    sceneManagerRef.value.stopModelMovement(modelId);
  }
};

// 旋转模型
const rotateModel = (modelId, rotation) => {
  if (sceneManagerRef.value) {
    sceneManagerRef.value.updateModelRotation(modelId, rotation);
  }
};

// 缩放模型
const scaleModel = (modelId, scale) => {
  if (sceneManagerRef.value) {
    sceneManagerRef.value.updateModelScale(modelId, scale);
  }
};

// 仿真模型动画
const playModelAnimation = (modelId, animationIndex) => {
  if (sceneManagerRef.value) {
    sceneManagerRef.value.playModelAnimation(modelId, animationIndex);
  }
};

// 执行平滑移动
const executeMovement = async () => {
  const { selectedModelId, targetPosition, movementSpeed } = movementControls.value;

  if (!selectedModelId) {
    alert('请选择一个模型');
    return;
  }

  try {
    movementControls.value.isMoving = true;
    await moveModelToPosition(selectedModelId, targetPosition, movementSpeed);
    movementControls.value.isMoving = false;
    console.log('移动完成');
  } catch (error) {
    console.error('移动失败:', error);
    movementControls.value.isMoving = false;
  }
};

// 停止当前移动
const stopCurrentMovement = () => {
  const { selectedModelId } = movementControls.value;
  if (selectedModelId) {
    stopModelMovement(selectedModelId);
    movementControls.value.isMoving = false;
  }
};

// 获取当前选中模型的位置（地面坐标系）
const getCurrentModelPosition = () => {
  const { selectedModelId } = movementControls.value;
  if (selectedModelId) {
    const position = getModelPosition(selectedModelId);
    if (position) {
      movementControls.value.targetPosition = {
        x: Math.round(position.x * 100) / 100,
        y: Math.round(position.y * 100) / 100, // 地面坐标系：Y=0表示地面
        z: Math.round(position.z * 100) / 100,
      };
      console.log('当前模型地面坐标:', position);
    }
  }
};

// 键盘控制模型移动
const handleKeyboardMovement = event => {
  // 如果自由视角模式启用，完全阻止模型移动事件处理
  if (freeCameraControls.value.enabled) {
    event.preventDefault();
    return;
  }

  // 在第一人称视角模式下，允许键盘控制目标模型
  if (!movementControls.value.keyboardControlEnabled || movementControls.value.isMoving) return;

  const { selectedModelId } = movementControls.value;
  if (!selectedModelId) return;

  const currentPos = getModelPosition(selectedModelId);
  if (!currentPos) return;

  const stepSize = keyboardControls.value.stepSize;
  let newPosition = { ...currentPos };

  switch (event.key.toLowerCase()) {
    case 'w': // 向前移动
    case 'arrowup':
      newPosition.z -= stepSize;
      break;
    case 's': // 向后移动
    case 'arrowdown':
      newPosition.z += stepSize;
      break;
    case 'a': // 向左移动
    case 'arrowleft':
      newPosition.x -= stepSize;
      break;
    case 'd': // 向右移动
    case 'arrowright':
      newPosition.x += stepSize;
      break;
    case 'q': // 向上移动
      newPosition.y += stepSize;
      break;
    case 'e': // 向下移动
      newPosition.y = Math.max(0, newPosition.y - stepSize); // 不能低于地面
      break;
    default:
      return; // 不是控制键，直接返回
  }

  // 执行移动
  console.log(`Keyboard movement: ${selectedModelId} from`, currentPos, 'to', newPosition);
  // 如果在第一人称视角模式下，添加额外的日志
  if (firstPersonView.value.enabled && firstPersonView.value.targetModelId === selectedModelId) {
    console.log('First person view: keyboard controlling target model');
  }

  moveModelToPosition(selectedModelId, newPosition, movementControls.value.movementSpeed);
  event.preventDefault();
};

// 示例：导入JSON配置
const importConfig = jsonConfig => {
  try {
    const config = JSON.parse(jsonConfig);
    if (config.scene) {
      Object.assign(sceneConfig, config.scene);
    }
    if (config.models) {
      models.value = config.models;
      totalModels.value = config.models.length;
    }
  } catch (error) {
    console.error('导入配置失败:', error);
  }
};

// 示例：从文件导入配置
const handleFileImport = event => {
  const file = event.target.files[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = e => {
    importConfig(e.target.result);
  };
  reader.readAsText(file);
};

// 切换自由视角模式
const toggleFreeCameraMode = enabled => {
  if (sceneManagerRef.value) {
    freeCameraControls.value.enabled = enabled;
    sceneManagerRef.value.toggleFreeCameraMode(enabled);

    // 检查冲突并显示警告
    if (enabled && movementControls.value.keyboardControlEnabled) {
      freeCameraControls.value.conflictWarning = true;
      // 自动禁用模型键盘控制以避免冲突
      movementControls.value.keyboardControlEnabled = false;
      console.log('自由视角模式已启用，已自动禁用模型键盘控制以避免WASD键冲突');
    } else {
      freeCameraControls.value.conflictWarning = false;
    }
  }
};

// 定期同步自由视角状态（用于处理ESC键等外部触发的状态变化）
const syncFreeCameraState = () => {
  if (sceneManagerRef.value && sceneManagerRef.value.getFreeCameraMode) {
    const actualState = sceneManagerRef.value.getFreeCameraMode();
    if (freeCameraControls.value.enabled !== actualState) {
      freeCameraControls.value.enabled = actualState;
      console.log('自由视角状态已同步:', actualState ? '启用' : '禁用');
      // 如果状态变为禁用，清除冲突警告
      if (!actualState) {
        freeCameraControls.value.conflictWarning = false;
      }
    }
  }
};

// 设置相机移动速度
const setCameraMovementSpeed = speed => {
  if (sceneManagerRef.value) {
    freeCameraControls.value.movementSpeed = speed;
    sceneManagerRef.value.setCameraMovementSpeed(speed);
  }
};

// 组件挂载时添加键盘事件监听和状态同步
onMounted(async () => {
  window.addEventListener('keydown', handleKeyboardMovement);
  console.log('键盘控制已启用：WASD或方向键移动，Q/E上下移动');

  // 为现有模型添加默认的信息卡片显示状态
  models.value.forEach(model => {
    if (model.showInfoCard === undefined) {
      // 使用 Vue.set 或直接赋值来确保响应式
      model.showInfoCard = true; // 默认显示信息卡片
      console.log(`📋 初始化模型 ${model.name} 的信息卡片状态为 true`);
    }
  });

  // 自动选择第一个可用模型
  autoSelectFirstModel();

  // 加载路径数据
  await loadPathData();

  // 初始化路线渲染器
  nextTick(() => {
    if (sceneManagerRef.value) {
      const scene = sceneManagerRef.value.getScene();
      if (scene) {
        routeRenderer.value = new RouteRenderer(scene);
        console.log('🛣️ 路线渲染器初始化完成');
      }
    }
  });

  // 添加地图右键事件监听（优化版）
  nextTick(() => {
    const sceneContainer = document.querySelector('.three-container');
    if (sceneContainer) {
      // 使用passive: false确保preventDefault生效，capture: true提高响应速度
      sceneContainer.addEventListener('contextmenu', handleMapRightClick, { passive: false, capture: true });
      sceneContainer.addEventListener('click', hideMapContextMenu, { passive: true });
      console.log('地图右键事件监听已添加');
    } else {
      console.warn('未找到.three-container元素');
    }
  });

  // 启动状态同步定时器
  const syncInterval = setInterval(syncFreeCameraState, 100); // 每100ms同步一次状态

  // 在组件卸载时清理定时器
  onBeforeUnmount(() => {
    clearInterval(syncInterval);
    window.removeEventListener('keydown', handleKeyboardMovement);

    // 清理路线渲染器
    if (routeRenderer.value) {
      routeRenderer.value.destroy();
      routeRenderer.value = null;
      console.log('🛣️ 路线渲染器已清理');
    }

    // 清理WebSocket连接
    if (websocketSimulator.value) {
      websocketSimulator.value.disconnect();
      websocketSimulator.value = null;
      websocketConnected.value = false;
      console.log('🔌 WebSocket连接已清理');
    }
  });
});

// 获取当前选中的模型对象
const getSelectedModel = () => {
  if (!sceneManagerRef.value) {
    console.log('SceneManager not available');
    return null;
  }

  const { selectedModelId } = movementControls.value;
  if (!selectedModelId) {
    console.log('No model selected');
    return null;
  }

  // 通过SceneManager获取模型对象
  try {
    // 直接从SceneManager的loadedModels中获取模型
    const loadedModels = sceneManagerRef.value.loadedModels;
    if (loadedModels && loadedModels[selectedModelId]) {
      console.log(`Found model ${selectedModelId}:`, loadedModels[selectedModelId]);
      return loadedModels[selectedModelId];
    }

    // 备用方法：通过modelRefs获取
    const modelRef = sceneManagerRef.value.modelRefs?.[selectedModelId];
    const model = modelRef?.getModel?.();
    if (model) {
      console.log(`Found model via modelRef ${selectedModelId}:`, model);
      return model;
    }

    console.log(`Model ${selectedModelId} not found in loadedModels or modelRefs`);
    return null;
  } catch (error) {
    console.error('Error getting selected model:', error);
    return null;
  }
};

// 获取当前选中的ModelComponent引用
const getSelectedModelComponent = () => {
  if (!sceneManagerRef.value) {
    console.log('SceneManager not available');
    return null;
  }

  const { selectedModelId } = movementControls.value;
  if (!selectedModelId) {
    console.log('No model selected');
    return null;
  }

  // 通过SceneManager获取ModelComponent引用
  try {
    const modelRef = sceneManagerRef.value.modelRefs?.[selectedModelId];
    if (modelRef) {
      console.log(`Found ModelComponent ${selectedModelId}:`, modelRef);
      return modelRef;
    }

    console.log(`ModelComponent ${selectedModelId} not found`);
    return null;
  } catch (error) {
    console.error('Error getting selected ModelComponent:', error);
    return null;
  }
};

// 处理路线状态变化
const handleRouteStatusChange = status => {
  console.log('Route status changed:', status);
  // 可以在这里添加状态变化的处理逻辑
};

// 处理路线位置更新
const handleRoutePositionUpdate = data => {
  console.log('Route position updated:', data);

  // 检查走行偏移报警
  if (data.position && data.modelId) {
    const expectedPosition = expectedPaths.value.get(data.modelId);
    if (expectedPosition) {
      checkDeviationAlerts(data.modelId, data.position, expectedPosition);
    }
  }

  // 如果当前模型正在进行路线模拟，且启用了第一视角，需要更新视角
  if (firstPersonView.value.enabled && firstPersonView.value.targetModelId) {
    // 第一视角会自动跟随模型位置和旋转，无需额外处理
    console.log('First person view following route simulation');
  }
};

// 设备管理事件处理
const handleDeviceAdded = (deviceConfig) => {
  console.log('Adding device to scene:', deviceConfig);

  // 创建新的模型配置
  const newModel = {
    id: deviceConfig.id,
    name: deviceConfig.name,
    path: deviceConfig.path,
    initialPosition: { ...deviceConfig.position },
    initialRotation: { ...deviceConfig.rotation },
    currentPosition: { ...deviceConfig.position },
    currentRotation: { ...deviceConfig.rotation },
    scale: deviceConfig.scale,
    defaultAnimation: 0,
    movementSpeed: 30.0,
    autoGroundPosition: true,
    material: {
      color: deviceConfig.material.color,
      roughness: deviceConfig.material.roughness,
      metalness: deviceConfig.material.metalness,
    },
    battery: deviceConfig.battery || Math.floor(Math.random() * 40) + 60,
    consume: 0, // 初始累计耗电量
    distance: 0, // 初始累计位移距离
    sensors: deviceConfig.sensors || [],
  };

  // 添加到模型数组
  models.value.push(newModel);
  totalModels.value = models.value.length;

  console.log('Device added successfully:', newModel);
};

const handleDeviceRemoved = (deviceId) => {
  console.log('Removing device from scene:', deviceId);

  // 从模型数组中移除
  const index = models.value.findIndex(model => model.id === deviceId);
  if (index !== -1) {
    models.value.splice(index, 1);
    totalModels.value = models.value.length;
    console.log('Device removed successfully:', deviceId);
  }
};

const handleDevicesImported = (config) => {
  console.log('Devices imported:', config);
  // 可以在这里添加额外的导入后处理逻辑
};

const handleDevicesExported = (config) => {
  console.log('Devices exported:', config);
  // 可以在这里添加额外的导出后处理逻辑
};

// 自动选择第一个可用模型作为移动控制目标
const autoSelectFirstModel = () => {
  if (models.value.length > 0 && !movementControls.value.selectedModelId) {
    movementControls.value.selectedModelId = models.value[0].id;
    console.log('自动选择第一个模型作为移动控制目标:', models.value[0].id);
  }
};

// 处理从左侧面板添加的模型
const handleModelAdded = (newModel) => {
  console.log('📋 Model added from left panel:', newModel);

  // 确保新模型有正确的信息卡片属性
  if (newModel.showInfoCard === undefined) {
    newModel.showInfoCard = true;
    console.log(`📋 为新模型 ${newModel.name} 设置默认信息卡片状态: true`);
  }

  // 模型已经在左侧面板中添加到models数组了，这里只需要更新总数
  totalModels.value = models.value.length;

  // 如果当前没有选中的移动控制目标，自动选择新添加的模型
  if (!movementControls.value.selectedModelId) {
    movementControls.value.selectedModelId = newModel.id;
    console.log('自动选择新添加的模型作为移动控制目标:', newModel.id);
  }

  console.log(`📋 模型添加完成: ${newModel.name}, 总数: ${totalModels.value}`);
};

// 处理从左侧面板导出的配置
const handleConfigExported = (config) => {
  console.log('Config exported from left panel:', config);
  // 可以在这里添加额外的导出后处理逻辑
};

// 处理从左侧面板导入的配置
const handleConfigImported = (config) => {
  console.log('Config imported from left panel:', config);
  // 更新总模型数量
  totalModels.value = models.value.length;
  // 可以在这里添加额外的导入后处理逻辑
};

// 处理右侧面板的移动控制事件
const handleRightPanelModelSelected = (modelId) => {
  console.log('Right panel model selected:', modelId);
  movementControls.value.selectedModelId = modelId;
};

const handleRightPanelMovementStarted = (data) => {
  console.log('Right panel movement started:', data);
  movementControls.value.isMoving = true;
  movementControls.value.selectedModelId = data.modelId;
  movementControls.value.targetPosition = data.targetPosition;
  movementControls.value.movementSpeed = data.speed;
};

const handleRightPanelMovementStopped = (data) => {
  console.log('Right panel movement stopped:', data);
  movementControls.value.isMoving = false;
};

// 处理车型选择变化
const handleVehicleTypeChanged = (vehicleType) => {
  console.log('🚗 接收到车型选择变化:', vehicleType);
  selectedTargetVehicleType.value = vehicleType;

  // 车型映射表
  const vehicleTypeNames = {
    '0': '扇形布料机',
    '1': '斗轮挖掘机',
    '2': '桥式转载机',
    '3': '中继转载机',
    '4': '移动转载机'
  };

  console.log(`✅ 目标车型已更新为: ${vehicleTypeNames[vehicleType] || '未知车型'} (${vehicleType})`);
};

// 处理场景设置变化
const handleSceneSettingsChanged = (settings) => {
  console.log('Scene settings changed:', settings);
  // 这里可以添加额外的场景设置处理逻辑
};

// 处理流畅动画设置变化
const handleSmoothAnimationChanged = (animationSettings) => {
  console.log('🎬 流畅动画设置已变化:', animationSettings);

  // 如果WebSocket模拟器已初始化，应用新设置
  if (websocketSimulator.value) {
    try {
      // 更新流畅动画启用状态
      if (websocketSimulator.value.setSmoothAnimation) {
        websocketSimulator.value.setSmoothAnimation(animationSettings.enabled);
      }

      // 更新流畅动画控制器配置
      if (websocketSimulator.value.smoothAnimationController) {
        const controller = websocketSimulator.value.smoothAnimationController;
        controller.options.bufferSize = animationSettings.bufferSize;
        controller.options.predictionTime = animationSettings.predictionTime;
        controller.options.smoothingFactor = animationSettings.smoothingFactor;

        console.log('✅ 流畅动画控制器配置已更新:', controller.options);
      }

      // 可以保存设置到本地存储
      localStorage.setItem('smoothAnimationSettings', JSON.stringify(animationSettings));

    } catch (error) {
      console.error('更新流畅动画设置失败:', error);
    }
  }
};

// 处理信息卡片显示切换
const handleInfoCardsToggled = (visible) => {
  console.log('📋 Info cards toggled:', visible);
  showInfoCards.value = visible;
  console.log('📋 3D信息卡片显示状态:', visible ? '开启' : '关闭');

  if (visible) {
    startModelPositionSync();
  } else {
    stopModelPositionSync();
  }
};

// 处理单个模型信息卡片切换
const handleModelInfoCardToggled = (data) => {
  console.log('📋 单个模型信息卡片状态变化:', data);

  // 查找对应的模型并更新状态
  const model = models.value.find(m => m.id === data.modelId);
  if (model) {
    model.showInfoCard = data.showInfoCard;
    console.log(`📋 已更新模型 ${model.name} 的信息卡片状态:`, model.showInfoCard);

    // 强制触发响应式更新
    nextTick(() => {
      console.log(`📋 强制更新后模型 ${model.name} 的信息卡片状态:`, model.showInfoCard);
    });
  }
};



// 模型位置同步定时器
let positionSyncInterval = null;

// 路径执行系统
const pathExecutionSystem = ref({
  isRunning: false,
  startTime: 0,
  currentTime: 0,
  pathData: [],
  vehicleStates: new Map(), // 存储每台车的状态
  speedMultiplier: 1 // 执行速度倍数
});

// 地图交互系统
const mapInteraction = ref({
  startPoint: null,
  endPoint: null,
  showContextMenu: false,
  contextMenuPosition: { x: 0, y: 0 },
  clickedPoint: null,
  startFlag: null, // 起点旗帜3D对象
  endFlag: null,   // 终点旗帜3D对象
  isSimulating: false // 仿真进行中状态
});

// 调度请求系统
const scheduleRequest = ref({
  isWaitingForCoordinate: false,
  coordinateType: null,
  processType: null,
  currentProcessType: null, // 当前选择的工艺类型
  flags: new Map() // 存储调度坐标小旗 key: coordinateType, value: flagObject
});

// 路线渲染系统
const routeRenderer = ref(null);

// 获取可用的路线设备ID列表
const availableRouteDeviceIds = computed(() => {
  if (routeRenderer.value) {
    return routeRenderer.value.getDeviceIds();
  }
  return [];
});

// 车型选择
const selectedTargetVehicleType = ref('0'); // 默认选择扇形布料机

// 防抖定时器
let rightClickDebounceTimer = null;

// 验证模型数据完整性
const validateModelData = (model) => {
  if (!model) {
    console.error('模型数据为空');
    return false;
  }

  if (!model.id) {
    console.error('模型缺少ID:', model);
    return false;
  }

  // 检查位置信息
  const hasPosition = (model.currentPosition &&
    (model.currentPosition.x !== undefined && model.currentPosition.z !== undefined)) ||
    (model.position &&
    (model.position.x !== undefined && model.position.z !== undefined));

  if (!hasPosition) {
    console.error('模型缺少位置信息:', model.id, model);
    return false;
  }

  return true;
};

// 创建旗帜3D模型
const createFlag = (position, color, type) => {
  const flagGroup = new THREE.Group();

  // 旗杆 - 圆柱体 (2倍大小)
  const poleGeometry = new THREE.CylinderGeometry(0.2, 0.2, 8, 8);
  const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }); // 棕色
  const pole = new THREE.Mesh(poleGeometry, poleMaterial);
  pole.position.y = 4; // 旗杆高度的一半 (原来2 -> 4)
  flagGroup.add(pole);

  // 旗帜 - 平面几何 (2倍大小)
  const flagGeometry = new THREE.PlaneGeometry(4, 2.4);
  const flagMaterial = new THREE.MeshLambertMaterial({
    color: color,
    side: THREE.DoubleSide,
    transparent: true,
    opacity: 0.9
  });
  const flag = new THREE.Mesh(flagGeometry, flagMaterial);
  flag.position.set(2, 6.8, 0); // 旗帜位置在旗杆顶部 (原来1, 3.4 -> 2, 6.8)
  flagGroup.add(flag);

  // 添加发光效果 (2倍大小)
  const glowGeometry = new THREE.PlaneGeometry(4.4, 2.8);
  const glowMaterial = new THREE.MeshBasicMaterial({
    color: color,
    transparent: true,
    opacity: 0.3,
    side: THREE.DoubleSide
  });
  const glow = new THREE.Mesh(glowGeometry, glowMaterial);
  glow.position.set(2, 6.8, 0.01);
  flagGroup.add(glow);

  // 添加文字标签 (2倍大小)
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  canvas.width = 512; // 原来256 -> 512
  canvas.height = 256; // 原来128 -> 256

  // 绘制文字 (2倍大小)
  context.fillStyle = 'white';
  context.font = 'bold 64px Arial'; // 原来32px -> 64px
  context.textAlign = 'center';
  context.textBaseline = 'middle';
  context.fillText(type === 'start' ? '起点' : '终点', 256, 128); // 原来128, 64 -> 256, 128

  const textTexture = new THREE.CanvasTexture(canvas);
  const textMaterial = new THREE.MeshBasicMaterial({
    map: textTexture,
    transparent: true,
    side: THREE.DoubleSide
  });
  const textGeometry = new THREE.PlaneGeometry(3, 1.5); // 原来1.5, 0.75 -> 3, 1.5
  const textMesh = new THREE.Mesh(textGeometry, textMaterial);
  textMesh.position.set(2, 6.8, 0.02); // 原来1, 3.4 -> 2, 6.8
  flagGroup.add(textMesh);

  // 设置旗帜位置
  flagGroup.position.set(position.x, position.y, position.z);

  // 添加用户数据标识
  flagGroup.userData = { type: 'flag', flagType: type };

  // 添加旗帜飘动动画
  const animateFlag = () => {
    const time = Date.now() * 0.002;
    flag.rotation.z = Math.sin(time) * 0.1; // 轻微摆动
    glow.rotation.z = Math.sin(time) * 0.1;
    textMesh.rotation.z = Math.sin(time) * 0.1;

    // 继续动画
    if (flagGroup.parent) {
      requestAnimationFrame(animateFlag);
    }
  };

  // 启动动画
  requestAnimationFrame(animateFlag);

  return flagGroup;
};

// 创建调度坐标小旗
const createScheduleFlag = (position, color, coordinateType) => {
  const flagGroup = new THREE.Group();

  // 旗杆 - 圆柱体 (稍小一些)
  const poleGeometry = new THREE.CylinderGeometry(0.15, 0.15, 6, 8);
  const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }); // 棕色
  const pole = new THREE.Mesh(poleGeometry, poleMaterial);
  pole.position.y = 3; // 旗杆高度的一半
  flagGroup.add(pole);

  // 旗帜 - 平面几何 (稍小一些)
  const flagGeometry = new THREE.PlaneGeometry(3, 1.8);
  const flagMaterial = new THREE.MeshLambertMaterial({
    color: color,
    side: THREE.DoubleSide,
    transparent: true,
    opacity: 0.9
  });
  const flag = new THREE.Mesh(flagGeometry, flagMaterial);
  flag.position.set(1.5, 5.1, 0); // 旗帜位置在旗杆顶部
  flagGroup.add(flag);

  // 添加发光效果
  const glowGeometry = new THREE.PlaneGeometry(3.3, 2.1);
  const glowMaterial = new THREE.MeshBasicMaterial({
    color: color,
    transparent: true,
    opacity: 0.3,
    side: THREE.DoubleSide
  });
  const glow = new THREE.Mesh(glowGeometry, glowMaterial);
  glow.position.set(1.5, 5.1, 0.01);
  flagGroup.add(glow);

  // 添加文字标签
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  canvas.width = 256;
  canvas.height = 128;

  // 绘制文字
  context.fillStyle = 'white';
  context.font = 'bold 24px Arial';
  context.textAlign = 'center';
  context.textBaseline = 'middle';

  // 根据坐标类型显示不同文字
  const labelText = getCoordinateTypeName(coordinateType).substring(0, 4); // 取前4个字符
  context.fillText(labelText, 128, 64);

  const textTexture = new THREE.CanvasTexture(canvas);
  const textMaterial = new THREE.MeshBasicMaterial({
    map: textTexture,
    transparent: true,
    side: THREE.DoubleSide
  });
  const textGeometry = new THREE.PlaneGeometry(2.4, 1.2);
  const textMesh = new THREE.Mesh(textGeometry, textMaterial);
  textMesh.position.set(1.5, 5.1, 0.02);
  flagGroup.add(textMesh);

  // 设置旗帜位置
  flagGroup.position.set(position.x, position.y || 0, position.z);

  // 添加用户数据标识
  flagGroup.userData = { type: 'scheduleFlag', flagType: coordinateType };

  // 添加旗帜飘动动画
  const animateFlag = () => {
    const time = Date.now() * 0.002;
    flag.rotation.z = Math.sin(time) * 0.1; // 轻微摆动
    glow.rotation.z = Math.sin(time) * 0.1;
    textMesh.rotation.z = Math.sin(time) * 0.1;

    // 继续动画
    if (flagGroup.parent) {
      requestAnimationFrame(animateFlag);
    }
  };

  // 启动动画
  requestAnimationFrame(animateFlag);

  return flagGroup;
};

// 添加起点旗帜
const addStartFlag = (position) => {
  if (!sceneManagerRef.value) return;

  const scene = sceneManagerRef.value.getScene();
  if (!scene) return;

  // 移除现有的起点旗帜
  removeStartFlag();

  // 创建绿色起点旗帜
  const startFlag = createFlag(position, 0x00ff00, 'start');
  scene.add(startFlag);
  mapInteraction.value.startFlag = startFlag;

  console.log('✅ 起点旗帜已添加:', position);
};

// 添加终点旗帜
const addEndFlag = (position) => {
  if (!sceneManagerRef.value) return;

  const scene = sceneManagerRef.value.getScene();
  if (!scene) return;

  // 移除现有的终点旗帜
  removeEndFlag();

  // 创建红色终点旗帜
  const endFlag = createFlag(position, 0xff0000, 'end');
  scene.add(endFlag);
  mapInteraction.value.endFlag = endFlag;

  console.log('🏁 终点旗帜已添加:', position);
};

// 移除起点旗帜
const removeStartFlag = () => {
  if (mapInteraction.value.startFlag && sceneManagerRef.value) {
    const scene = sceneManagerRef.value.getScene();
    if (scene) {
      scene.remove(mapInteraction.value.startFlag);

      // 清理几何体和材质
      mapInteraction.value.startFlag.traverse((child) => {
        if (child.geometry) child.geometry.dispose();
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(material => {
              if (material.map) material.map.dispose();
              material.dispose();
            });
          } else {
            if (child.material.map) child.material.map.dispose();
            child.material.dispose();
          }
        }
      });

      mapInteraction.value.startFlag = null;
      console.log('起点旗帜已移除');
    }
  }
};

// 移除终点旗帜
const removeEndFlag = () => {
  if (mapInteraction.value.endFlag && sceneManagerRef.value) {
    const scene = sceneManagerRef.value.getScene();
    if (scene) {
      scene.remove(mapInteraction.value.endFlag);

      // 清理几何体和材质
      mapInteraction.value.endFlag.traverse((child) => {
        if (child.geometry) child.geometry.dispose();
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(material => {
              if (material.map) material.map.dispose();
              material.dispose();
            });
          } else {
            if (child.material.map) child.material.map.dispose();
            child.material.dispose();
          }
        }
      });

      mapInteraction.value.endFlag = null;
      console.log('终点旗帜已移除');
    }
  }
};

// 开始模型位置同步
const startModelPositionSync = () => {
  if (positionSyncInterval) {
    clearInterval(positionSyncInterval);
  }

  positionSyncInterval = setInterval(() => {
    syncModelPositions();
    // 同时触发相机位置检查，确保卡片朝向更新
    triggerCameraUpdate();
  }, 50); // 每50ms同步一次位置，确保实时性
};

// 停止模型位置同步
const stopModelPositionSync = () => {
  if (positionSyncInterval) {
    clearInterval(positionSyncInterval);
    positionSyncInterval = null;
  }
};

// 同步所有模型的位置
const syncModelPositions = () => {
  if (!sceneManagerRef.value || !showInfoCards.value) {
    return;
  }

  models.value.forEach(model => {
    const modelMesh = getModelMesh(model.id);
    if (modelMesh) {
      // 获取模型的世界坐标
      const worldPosition = new THREE.Vector3();
      modelMesh.getWorldPosition(worldPosition);

      // 更新模型数据中的位置
      if (model.currentPosition) {
        model.currentPosition.x = worldPosition.x;
        model.currentPosition.y = worldPosition.y;
        model.currentPosition.z = worldPosition.z;
      } else {
        model.currentPosition = {
          x: worldPosition.x,
          y: worldPosition.y,
          z: worldPosition.z
        };
      }

      // 获取模型的旋转
      if (modelMesh.rotation) {
        if (model.currentRotation) {
          model.currentRotation.x = modelMesh.rotation.x * (180 / Math.PI); // 转换为度
          model.currentRotation.y = modelMesh.rotation.y * (180 / Math.PI);
          model.currentRotation.z = modelMesh.rotation.z * (180 / Math.PI);
        } else {
          model.currentRotation = {
            x: modelMesh.rotation.x * (180 / Math.PI),
            y: modelMesh.rotation.y * (180 / Math.PI),
            z: modelMesh.rotation.z * (180 / Math.PI)
          };
        }
      }
    }
  });
};

// 加载路径数据
const loadPathData = async () => {
  try {
    const response = await fetch('/src/views/route_simulatton/components/path2.json');

    const pathData = await response.json();
    pathExecutionSystem.value.pathData = pathData;

    // 初始化车辆状态（保持车辆原有电量）
    pathData.forEach(vehicleData => {
      const model = models.value.find(m => m.id === vehicleData.device_id);
      const currentPower = model?.battery || 100;

      pathExecutionSystem.value.vehicleStates.set(vehicleData.device_id, {
        currentPathIndex: 0,
        isMoving: false,
        hasStarted: false,
        currentPosition: null,
        currentRotation: null,
        targetPosition: null,
        targetRotation: null,
        moveStartTime: 0,
        moveDuration: 0,
        currentPower: currentPower,
        startPower: currentPower, // 记录车辆加载时的电量
        initialPower: currentPower, // 记录当前路径点开始时的电量
        currentConsume: 0, // 当前累计耗电量
        initialConsume: 0, // 当前路径点开始时的累计耗电量
        lastLogTime: 0
      });
    });

    console.log('路径数据加载成功:', pathData);
    return pathData;
  } catch (error) {
    console.error('加载路径数据失败:', error);
    return [];
  }
};

// 开始路径执行
const startPathExecution = () => {
  if (pathExecutionSystem.value.isRunning) {
    console.log('路径执行已在运行中');
    return;
  }

  pathExecutionSystem.value.isRunning = true;
  pathExecutionSystem.value.startTime = Date.now();
  pathExecutionSystem.value.currentTime = 0;

  // 重置所有车辆状态（保持车辆原有电量）
  pathExecutionSystem.value.vehicleStates.forEach((state, deviceId) => {
    const model = models.value.find(m => m.id === deviceId);
    // 保持车辆当前的电量，不重置为100
    const currentPower = model?.battery || 100;

    state.currentPathIndex = 0;
    state.isMoving = false;
    state.hasStarted = false;
    state.currentPosition = null;
    state.currentRotation = null;
    state.targetPosition = null;
    state.targetRotation = null;
    state.moveStartTime = 0;
    state.moveDuration = 0;
    state.currentPower = currentPower;
    state.startPower = currentPower; // 记录车辆加载时的电量
    state.initialPower = currentPower; // 记录当前路径点开始时的电量
    state.currentConsume = model?.consume || 0; // 当前累计耗电量
    state.initialConsume = model?.consume || 0; // 当前路径点开始时的累计耗电量

    console.log(`🚗 车辆 ${deviceId} 初始电量: ${currentPower}%, 累计耗电: ${model?.consume || 0}`);
  });

  console.log('🚀 开始执行路径，车辆数量:', pathExecutionSystem.value.pathData.length);
  console.log('✅ 路径执行系统已启动，不受UI状态影响');

  // 启动路径更新循环
  startPathUpdateLoop();
};

// 停止路径执行
const stopPathExecution = () => {
  pathExecutionSystem.value.isRunning = false;
  pathExecutionSystem.value.speedMultiplier = 1; // 重置速度倍数
  console.log('⏹️ 路径执行已停止，速度倍数已重置为1x');
};

// 右侧面板状态（从右侧面板组件获取）
const rightPanelCollapsed = ref(false);

// 监听右侧面板状态变化，调试是否影响路径执行
watch(() => rightPanelCollapsed.value, (newValue, oldValue) => {
  console.log(`📱 右侧面板状态变化: ${oldValue} -> ${newValue}`);
  console.log(`🔄 路径执行状态: ${pathExecutionSystem.value.isRunning ? '运行中' : '已停止'}`);

  if (pathExecutionSystem.value.isRunning) {
    console.log('⚠️ 路径执行正在进行中，面板状态变化不应影响执行');
  }
});

// 路径更新循环
const startPathUpdateLoop = () => {
  console.log('🔄 启动路径执行循环 - 独立于UI状态');

  const updateLoop = () => {
    // 检查路径执行状态，确保独立于UI状态
    if (!pathExecutionSystem.value.isRunning) {
      console.log('⏹️ 路径执行已停止，退出循环');
      return;
    }

    try {
      // 计算当前时间（秒），应用速度倍数
      const realElapsedTime = (Date.now() - pathExecutionSystem.value.startTime) / 1000;
      pathExecutionSystem.value.currentTime = realElapsedTime * pathExecutionSystem.value.speedMultiplier;

      // 更新所有车辆
      updateAllVehicles();

      // 继续循环 - 确保循环持续进行，不受UI状态影响
      requestAnimationFrame(updateLoop);
    } catch (error) {
      console.error('路径更新循环出错:', error);
      // 即使出错也要继续循环，除非手动停止
      if (pathExecutionSystem.value.isRunning) {
        requestAnimationFrame(updateLoop);
      }
    }
  };

  requestAnimationFrame(updateLoop);
};

// 更新所有车辆
const updateAllVehicles = () => {
  pathExecutionSystem.value.pathData.forEach(vehicleData => {
    updateVehicle(vehicleData);
  });
};

// 更新单个车辆
const updateVehicle = (vehicleData) => {
  const { device_id, start_time, path } = vehicleData;
  const currentTime = pathExecutionSystem.value.currentTime;
  const vehicleState = pathExecutionSystem.value.vehicleStates.get(device_id);

  if (!vehicleState) return;

  // 检查是否到了开始时间
  if (!vehicleState.hasStarted && currentTime >= start_time) {
    vehicleState.hasStarted = true;
    console.log(`车辆 ${device_id} 开始移动，时间: ${currentTime.toFixed(2)}s`);
  }

  if (!vehicleState.hasStarted) return;

  // 检查是否有路径点
  if (vehicleState.currentPathIndex >= path.length) return;

  const currentPathPoint = path[vehicleState.currentPathIndex];

  // 如果还没开始移动到当前路径点
  if (!vehicleState.isMoving) {
    startMovingToPathPoint(device_id, currentPathPoint, currentTime);
  } else {
    // 继续移动到目标点
    updateMovementToPathPoint(device_id, currentPathPoint, currentTime);
  }
};

// 开始移动到路径点
const startMovingToPathPoint = (deviceId, pathPoint, currentTime) => {
  const vehicleState = pathExecutionSystem.value.vehicleStates.get(deviceId);
  if (!vehicleState) return;

  // 获取当前模型
  const model = models.value.find(m => m.id === deviceId);
  if (!model) return;

  // 设置起始位置（当前位置或初始位置）
  vehicleState.currentPosition = vehicleState.currentPosition || {
    x: model.currentPosition?.x || 0,
    y: model.currentPosition?.y || 0,
    z: model.currentPosition?.z || 0
  };

  vehicleState.currentRotation = vehicleState.currentRotation || {
    x: model.currentRotation?.x || 0,
    y: model.currentRotation?.y || 0,
    z: model.currentRotation?.z || 0
  };

  // 记录这个路径点开始时的电量（用于线性插值）
  vehicleState.initialPower = model.battery || 100;

  // 记录这个路径点开始时的累计耗电量（用于线性插值）
  vehicleState.initialConsume = model.consume || 0;

  console.log(`🔋 车辆 ${deviceId} 开始路径点 ${vehicleState.currentPathIndex}: 电量 ${vehicleState.initialPower}% -> ${pathPoint.power}%, 累计耗电 ${vehicleState.initialConsume} -> ${pathPoint.consume || 0} (${vehicleState.moveDuration}秒)`);

  // 设置目标位置
  vehicleState.targetPosition = { ...pathPoint.position };
  vehicleState.targetRotation = { ...pathPoint.rotation };
  vehicleState.moveStartTime = currentTime;
  vehicleState.moveDuration = pathPoint.duration / 1000; // 转换为秒
  vehicleState.isMoving = true;

  console.log(`车辆 ${deviceId} 开始移动到路径点 ${vehicleState.currentPathIndex}:`, {
    from: vehicleState.currentPosition,
    to: vehicleState.targetPosition,
    duration: vehicleState.moveDuration,
    powerChange: `${vehicleState.startPower}% -> ${pathPoint.power}%`
  });
};

// 更新移动到路径点
const updateMovementToPathPoint = (deviceId, pathPoint, currentTime) => {
  const vehicleState = pathExecutionSystem.value.vehicleStates.get(deviceId);
  if (!vehicleState) return;

  const model = models.value.find(m => m.id === deviceId);
  if (!model) return;

  const elapsedTime = currentTime - vehicleState.moveStartTime;
  const progress = Math.min(elapsedTime / vehicleState.moveDuration, 1.0);

  // 线性插值计算当前位置
  const currentPos = {
    x: vehicleState.currentPosition.x + (vehicleState.targetPosition.x - vehicleState.currentPosition.x) * progress,
    y: vehicleState.currentPosition.y + (vehicleState.targetPosition.y - vehicleState.currentPosition.y) * progress,
    z: vehicleState.currentPosition.z + (vehicleState.targetPosition.z - vehicleState.currentPosition.z) * progress
  };

  // 线性插值计算当前旋转
  const currentRot = {
    x: vehicleState.currentRotation.x + (vehicleState.targetRotation.x - vehicleState.currentRotation.x) * progress,
    y: vehicleState.currentRotation.y + (vehicleState.targetRotation.y - vehicleState.currentRotation.y) * progress,
    z: vehicleState.currentRotation.z + (vehicleState.targetRotation.z - vehicleState.currentRotation.z) * progress
  };

  // 更新模型位置和旋转
  model.currentPosition = { ...currentPos };
  model.currentRotation = { ...currentRot };

  // 线性插值计算当前电量
  const startPower = vehicleState.initialPower; // 使用路径开始时的初始电量
  const endPower = pathPoint.power; // 目标电量
  const currentPower = startPower + (endPower - startPower) * progress;

  // 更新电量（实时线性减少）
  model.battery = Math.round(currentPower * 10) / 10; // 保留1位小数
  vehicleState.currentPower = model.battery;

  // 更新累计耗电量（如果路径点包含consume字段）
  if (pathPoint.consume !== undefined) {
    const startConsume = vehicleState.initialConsume || 0;
    const endConsume = pathPoint.consume;
    const currentConsume = startConsume + (endConsume - startConsume) * progress;
    model.consume = Math.round(currentConsume * 10) / 10; // 保留1位小数
    vehicleState.currentConsume = model.consume;
  }

  // 实时更新车辆消耗信息到右侧面板（每100ms更新一次）
  const updateInterval = 100; // 100ms
  if (!vehicleState.lastDisplayUpdateTime || (currentTime - vehicleState.lastDisplayUpdateTime) >= updateInterval) {
    vehicleState.lastDisplayUpdateTime = currentTime;

    // 创建实时路径点数据用于显示
    const realtimePathPoint = {
      power: model.battery,
      consume: model.consume || 0
    };

    updateVehicleConsumptionDisplay(deviceId, realtimePathPoint, vehicleState);
  }

  // 电量变化的调试信息（每秒输出一次）
  if (Math.floor(currentTime) !== Math.floor(vehicleState.lastLogTime || 0)) {
    vehicleState.lastLogTime = currentTime;
    const consumedPower = startPower - model.battery;
    const currentConsume = model.consume || 0;
    console.log(`🔋 车辆 ${deviceId}: 电量${model.battery.toFixed(1)}% (初始:${startPower}% 目标:${endPower}% 已消耗:${consumedPower.toFixed(1)}%) 累计耗电:${currentConsume.toFixed(1)} 进度:${(progress * 100).toFixed(1)}%`);
  }

  // 检查是否到达目标点
  if (progress >= 1.0) {
    vehicleState.currentPosition = { ...vehicleState.targetPosition };
    vehicleState.currentRotation = { ...vehicleState.targetRotation };
    vehicleState.isMoving = false;
    vehicleState.currentPathIndex++;

    // 确保最终电量准确
    model.battery = pathPoint.power;
    vehicleState.currentPower = pathPoint.power;

    // 确保最终累计耗电量准确
    if (pathPoint.consume !== undefined) {
      model.consume = pathPoint.consume;
      vehicleState.currentConsume = pathPoint.consume;
      vehicleState.initialConsume = pathPoint.consume; // 为下一个路径点更新初始累计耗电量
    }

    // 为下一个路径点更新初始电量（当前电量作为下一段的起始电量）
    vehicleState.initialPower = pathPoint.power;

    // 更新车辆消耗信息到右侧面板
    updateVehicleConsumptionDisplay(deviceId, pathPoint, vehicleState);

    console.log(`✅ 车辆 ${deviceId} 到达路径点 ${vehicleState.currentPathIndex - 1}，最终电量: ${pathPoint.power}%，累计耗电: ${pathPoint.consume || 0}`);

    // 检查是否完成所有路径点
    const vehicleData = pathExecutionSystem.value.pathData.find(v => v.device_id === deviceId);
    if (vehicleState.currentPathIndex >= vehicleData.path.length) {
      const finalConsume = pathPoint.consume || vehicleData.consume || 0;
      console.log(`🏁 车辆 ${deviceId} 完成所有路径点，最终电量: ${pathPoint.power}%，总累计耗电: ${finalConsume}`);
    }
  }
};

// 相机位置缓存，用于检测变化
let lastCameraPosition = new THREE.Vector3();
let lastCameraRotation = new THREE.Euler();

// 触发相机更新检查
const triggerCameraUpdate = () => {
  if (!sceneManagerRef.value?.camera) return;

  const camera = sceneManagerRef.value.camera;
  const currentPosition = camera.position.clone();
  const currentRotation = camera.rotation.clone();

  // 检查相机位置或旋转是否发生变化
  const positionChanged = !currentPosition.equals(lastCameraPosition);
  const rotationChanged = !currentRotation.equals(lastCameraRotation);

  if (positionChanged || rotationChanged) {
    // 更新缓存
    lastCameraPosition.copy(currentPosition);
    lastCameraRotation.copy(currentRotation);

    // 强制触发相机变化事件（通过修改引用）
    // 这会让Vue的响应式系统检测到变化
    if (showInfoCards.value) {
      // 可以在这里添加额外的相机变化处理逻辑
      console.log('相机位置发生变化，触发卡片朝向更新');
    }
  }
};

// 卡片位置更新定时器
let cardUpdateInterval = null;

// 开始卡片位置更新
const startCardPositionUpdates = () => {
  if (cardUpdateInterval) {
    clearInterval(cardUpdateInterval);
  }

  // 使用requestAnimationFrame进行更平滑的更新
  const updateLoop = () => {
    if (showInfoCards.value) {
      updateModelScreenPositions();
      cardUpdateInterval = requestAnimationFrame(updateLoop);
    }
  };

  cardUpdateInterval = requestAnimationFrame(updateLoop);
};

// 停止卡片位置更新
const stopCardPositionUpdates = () => {
  if (cardUpdateInterval) {
    cancelAnimationFrame(cardUpdateInterval);
    cardUpdateInterval = null;
  }
};

// 将3D世界坐标转换为屏幕坐标
const worldToScreen = (worldPosition, camera, renderer) => {
  if (!camera || !renderer || !worldPosition) {
    return { x: 0, y: 0, visible: false };
  }

  try {
    // 创建3D向量，在模型上方显示卡片
    const vector = new THREE.Vector3(
      worldPosition.x,
      worldPosition.y + 8, // 在模型上方8个单位
      worldPosition.z
    );

    // 将世界坐标投影到屏幕坐标
    vector.project(camera);

    // 检查是否在相机后面
    if (vector.z > 1) {
      return { x: 0, y: 0, visible: false };
    }

    // 获取渲染器尺寸
    const canvas = renderer.domElement;
    const rect = canvas.getBoundingClientRect();

    // 转换为屏幕坐标
    const x = (vector.x * 0.5 + 0.5) * rect.width + rect.left;
    const y = (vector.y * -0.5 + 0.5) * rect.height + rect.top;

    // 检查是否在屏幕范围内（给一些边距）
    const margin = 50;
    const visible =
      x >= (rect.left - margin) &&
      x <= (rect.right + margin) &&
      y >= (rect.top - margin) &&
      y <= (rect.bottom + margin) &&
      vector.z <= 1; // 确保在相机前面

    return { x, y, visible };
  } catch (error) {
    console.error('坐标转换失败:', error);
    return { x: 0, y: 0, visible: false };
  }
};

// 更新所有模型的屏幕位置
const updateModelScreenPositions = () => {
  if (!sceneManagerRef.value || !showInfoCards.value) {
    return;
  }

  try {
    const camera = sceneManagerRef.value.camera;
    const renderer = sceneManagerRef.value.renderer;

    if (!camera || !renderer) {
      return;
    }

    models.value.forEach(model => {
      if (model.currentPosition) {
        const screenPos = worldToScreen(model.currentPosition, camera, renderer);
        modelScreenPositions.value.set(model.id, screenPos);
      }
    });
  } catch (error) {
    console.error('更新模型屏幕位置失败:', error);
  }
};

// 获取模型的屏幕位置
const getModelScreenPosition = (model) => {
  return modelScreenPositions.value.get(model.id) || { x: 0, y: 0, visible: false };
};

// 获取3D卡片的可见性
const getCardVisibility = (model) => {
  if (!model) {
    console.warn('📋 getCardVisibility: model is undefined');
    return false;
  }

  // 确保模型有 showInfoCard 属性
  if (model.showInfoCard === undefined) {
    model.showInfoCard = true; // 默认显示
    console.log(`📋 初始化模型 ${model.name} 的 showInfoCard 属性为 true`);
  }

  const globalVisible = showInfoCards.value;
  const modelVisible = model.showInfoCard === true;
  const result = globalVisible && modelVisible;

  console.log(`📋 模型 ${model.name || 'Unknown'} 卡片可见性检查:`, {
    modelId: model.id,
    globalVisible,
    modelVisible,
    showInfoCard: model.showInfoCard,
    result
  });

  return result;
};

// 获取模型的3D网格对象
const getModelMesh = (modelId) => {
  if (!sceneManagerRef.value) {
    return null;
  }

  try {
    // 尝试从SceneManager的loadedModels中获取
    const loadedModels = sceneManagerRef.value.loadedModels;
    if (loadedModels && loadedModels[modelId]) {
      return loadedModels[modelId];
    }

    // 尝试从modelRefs中获取
    const modelRef = sceneManagerRef.value.modelRefs?.[modelId];
    if (modelRef && modelRef.getModel) {
      return modelRef.getModel();
    }

    // 如果都没有，尝试在场景中查找
    if (scene.value) {
      let foundModel = null;
      scene.value.traverse((child) => {
        if (child.userData && child.userData.modelId === modelId) {
          foundModel = child;
        }
      });
      return foundModel;
    }

    return null;
  } catch (error) {
    console.error(`获取模型网格失败 ${modelId}:`, error);
    return null;
  }
};

// 切换控制台显示/隐藏
const toggleControlPanel = () => {
  controlPanelVisible.value = !controlPanelVisible.value;
};

// 监听控制台状态变化，自动调整3D场景大小
watch(controlPanelVisible, () => {
  // 延迟调用forceResize，确保DOM更新完成
  nextTick(() => {
    if (sceneManagerRef.value && sceneManagerRef.value.forceResize) {
      console.log('Control panel visibility changed, resizing 3D scene...');
      sceneManagerRef.value.forceResize();
    }
  });
});

// 切换面板展开/关闭状态
const togglePanel = panelName => {
  panelStates.value[panelName] = !panelStates.value[panelName];
};

// 设置相机预设位置
const setCameraPreset = (presetName) => {
  if (firstPersonView.value.presets[presetName]) {
    const preset = firstPersonView.value.presets[presetName];
    firstPersonView.value.cameraOffset = { ...preset.camera };
    firstPersonView.value.lookAtOffset = { ...preset.lookAt };
    firstPersonView.value.currentPreset = presetName;

    console.log('📷 相机预设已切换:', presetName, preset);

    // 如果第一人称视角已启用，立即更新相机位置
    if (firstPersonView.value.enabled) {
      updateFirstPersonCamera();
    }
  }
};

// 设置自定义相机位置
const setCustomCameraPosition = (cameraPos, lookAtPos) => {
  firstPersonView.value.cameraOffset = { ...cameraPos };
  firstPersonView.value.lookAtOffset = { ...lookAtPos };
  firstPersonView.value.currentPreset = 'custom';

  // 更新自定义预设
  firstPersonView.value.presets.custom.camera = { ...cameraPos };
  firstPersonView.value.presets.custom.lookAt = { ...lookAtPos };

  console.log('🎯 自定义相机位置已设置:', { camera: cameraPos, lookAt: lookAtPos });

  // 如果第一人称视角已启用，立即更新相机位置
  if (firstPersonView.value.enabled) {
    updateFirstPersonCamera();
  }
};

// 启用第一视角
const enableFirstPersonView = modelId => {
  if (!sceneManagerRef.value) {
    console.warn('SceneManager not available');
    return;
  }

  // 如果已经在第一视角模式，先退出
  if (firstPersonView.value.enabled) {
    disableFirstPersonView();
  }

  // 获取目标模型
  const targetModel = getSelectedModelByIdForFirstPerson(modelId);
  const camera = sceneManagerRef.value?.getCamera?.();

  if (!targetModel || !camera) {
    console.warn(`Model ${modelId} or camera not found for first person view`);
    return;
  }

  // 保存原始相机状态
  firstPersonView.value.originalCameraPosition.copy(camera.position);
  firstPersonView.value.originalCameraRotation.copy(camera.rotation);

  // 获取控制器并保存原始状态
  const controls = sceneManagerRef.value.getControls?.();
  if (controls) {
    firstPersonView.value.originalControlsTarget.copy(controls.target);
  }

  // 启用第一人称模式（这会自动禁用轨道控制器和自由视角）
  if (sceneManagerRef.value.setFirstPersonMode) {
    sceneManagerRef.value.setFirstPersonMode(true);
  }

  // 设置移动控制的目标模型为第一人称视角的目标模型
  // 这样键盘控制就会控制第一人称视角的目标模型
  movementControls.value.selectedModelId = modelId;

  firstPersonView.value.enabled = true;
  firstPersonView.value.targetModelId = modelId;

  // 确保使用驾驶员视角作为默认视角
  if (firstPersonView.value.currentPreset !== 'driver') {
    setCameraPreset('driver');
  }

  // 开始第一视角更新循环
  startFirstPersonUpdate();

  console.log(`First person view enabled for model: ${modelId} with driver preset`);
  console.log(`Movement controls now target model: ${modelId}`);
};

// 获取指定ID的模型（用于第一视角）
const getSelectedModelByIdForFirstPerson = modelId => {
  if (!sceneManagerRef.value) return null;

  try {
    // 从SceneManager获取模型
    const loadedModels = sceneManagerRef.value.loadedModels;
    if (loadedModels && loadedModels[modelId]) {
      return loadedModels[modelId];
    }

    // 备用方法
    const modelRef = sceneManagerRef.value.modelRefs?.[modelId];
    return modelRef?.getModel?.() || null;
  } catch (error) {
    console.error('Error getting model for first person view:', error);
    return null;
  }
};

// 开始第一视角更新
const startFirstPersonUpdate = () => {
  if (firstPersonView.value.updateInterval) {
    cancelAnimationFrame(firstPersonView.value.updateInterval);
  }

  // 每帧更新第一视角
  const updateFirstPersonView = () => {
    if (!firstPersonView.value.enabled || !firstPersonView.value.targetModelId) {
      return;
    }

    updateFirstPersonCamera();
  };

  // 更新第一人称相机位置和朝向
  const updateFirstPersonCamera = () => {
    const targetModel = getSelectedModelByIdForFirstPerson(firstPersonView.value.targetModelId);
    const camera = sceneManagerRef.value?.getCamera?.();

    if (!targetModel || !camera) {
      return;
    }

    // 获取模型的世界变换矩阵
    targetModel.updateMatrixWorld();
    const modelMatrix = targetModel.matrixWorld;

    // 将相机偏移位置从模型坐标系转换到世界坐标系
    const cameraLocalPos = new THREE.Vector3(
      firstPersonView.value.cameraOffset.x,
      firstPersonView.value.cameraOffset.y,
      firstPersonView.value.cameraOffset.z
    );
    const cameraWorldPos = cameraLocalPos.clone().applyMatrix4(modelMatrix);

    // 将朝向目标位置从模型坐标系转换到世界坐标系
    const lookAtLocalPos = new THREE.Vector3(
      firstPersonView.value.lookAtOffset.x,
      firstPersonView.value.lookAtOffset.y,
      firstPersonView.value.lookAtOffset.z
    );
    const lookAtWorldPos = lookAtLocalPos.clone().applyMatrix4(modelMatrix);

    // 设置相机位置和朝向
    camera.position.copy(cameraWorldPos);
    camera.lookAt(lookAtWorldPos);

    // console.log('📷 相机位置更新:', {
    //   modelPos: targetModel.position,
    //   cameraLocal: cameraLocalPos,
    //   cameraWorld: cameraWorldPos,
    //   lookAtLocal: lookAtLocalPos,
    //   lookAtWorld: lookAtWorldPos
    // });
  };

  // 使用requestAnimationFrame进行实时更新
  const animate = () => {
    if (firstPersonView.value.enabled) {
      updateFirstPersonView();
      firstPersonView.value.updateInterval = requestAnimationFrame(animate);
    }
  };

  animate();
};

// 切换第一视角
const toggleFirstPersonView = modelId => {
  if (firstPersonView.value.enabled && firstPersonView.value.targetModelId === modelId) {
    // 如果当前模型已经是第一视角，则退出
    disableFirstPersonView();
  } else {
    // 启用指定模型的第一视角
    enableFirstPersonView(modelId);
  }
};

// 处理模型右键点击事件
const handleModelRightClicked = data => {
  console.log('Model right-clicked in 3D scene:', data);
  const { modelId, mouseX, mouseY } = data;

  // 检查模型是否存在（移除loadingStatus检查，因为新添加的模型可能没有在loadingStatus中）
  const modelExists = models.value.some(model => model.id === modelId);
  if (!modelExists) {
    console.warn(`Model ${modelId} not found in models array`);
    return;
  }

  // 显示右键菜单
  showContextMenu(modelId, mouseX, mouseY);
};

// 处理地图右键点击事件（高性能优化版）
const handleMapRightClick = (event) => {
  const startTime = performance.now();

  event.preventDefault();
  event.stopPropagation();

  // 防抖处理
  if (rightClickDebounceTimer) {
    clearTimeout(rightClickDebounceTimer);
  }

  // 立即显示菜单位置，提升响应速度
  mapInteraction.value.contextMenuPosition = {
    x: event.clientX,
    y: event.clientY
  };
  mapInteraction.value.showContextMenu = true;
  mapInteraction.value.clickedPoint = null; // 重置坐标，显示加载状态

  // 使用防抖延迟计算坐标
  rightClickDebounceTimer = setTimeout(() => {
    requestAnimationFrame(() => {
      const clickedPosition = getWorldPositionFromMouse(event);
      if (!clickedPosition) {
        console.warn('无法获取地图坐标');
        hideMapContextMenu();
        return;
      }

      // 设置y为0，只记录x,z，减少小数位数提高性能
      const mapPoint = {
        x: Math.round(clickedPosition.x * 10) / 10,
        y: 0,
        z: Math.round(clickedPosition.z * 10) / 10
      };

      mapInteraction.value.clickedPoint = mapPoint;

      const endTime = performance.now();
      console.log(`地图右键响应时间: ${(endTime - startTime).toFixed(2)}ms, 坐标:`, mapPoint);
    });
  }, 50); // 50ms防抖延迟
};

// 预创建射线投射器和地面平面，避免重复创建
const raycaster = new THREE.Raycaster();
const groundPlane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);
const intersectPoint = new THREE.Vector3();
const mouseCoords = new THREE.Vector2();

// 从鼠标位置获取世界坐标（优化版）
const getWorldPositionFromMouse = (event) => {
  if (!sceneManagerRef.value) return null;

  const camera = sceneManagerRef.value.getCamera();
  const renderer = sceneManagerRef.value.getRenderer();
  if (!camera || !renderer) return null;

  // 获取鼠标在canvas中的位置（优化：减少DOM查询）
  const canvas = renderer.domElement;
  const rect = canvas.getBoundingClientRect();

  // 复用Vector2对象
  mouseCoords.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouseCoords.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  // 复用射线投射器
  raycaster.setFromCamera(mouseCoords, camera);

  // 与地面相交（y=0平面）
  raycaster.ray.intersectPlane(groundPlane, intersectPoint);

  // 返回新的Vector3对象，避免引用问题
  return intersectPoint.clone();
};

// 设置起点（优化版）
const setStartPoint = () => {
  if (mapInteraction.value.clickedPoint) {
    mapInteraction.value.startPoint = { ...mapInteraction.value.clickedPoint };
    console.log('✅ 起点已设置:', mapInteraction.value.startPoint);

    // 添加绿色起点旗帜
    nextTick(() => {
      addStartFlag(mapInteraction.value.startPoint);
    });
  }
  hideMapContextMenu();
};

// 清除起点
const clearStartPoint = () => {
  mapInteraction.value.startPoint = null;
  removeStartFlag();
  console.log('起点已清除');
};

// 清除终点
const clearEndPoint = () => {
  mapInteraction.value.endPoint = null;
  removeEndFlag();
  console.log('终点已清除');
};

// 测试电量变化效果（可选的调试函数）
const testBatteryChange = () => {
  console.log('🧪 测试电量变化效果:');
  models.value.forEach(model => {
    console.log(`车辆 ${model.id}: 当前电量 ${model.battery || 100}%`);
  });
};

// 更新执行速度倍数
const updateSpeedMultiplier = (multiplier) => {
  pathExecutionSystem.value.speedMultiplier = multiplier;

  // 根据速度等级显示不同的提示
  if (multiplier >= 80) {
    console.log(`🔥 超极速模式: ${multiplier}x (极高速度)`);
  } else if (multiplier >= 50) {
    console.log(`⚡ 极速模式: ${multiplier}x (快速验证)`);
  } else if (multiplier >= 10) {
    console.log(`🚀 高速模式: ${multiplier}x (长路径测试)`);
  } else if (multiplier > 1) {
    console.log(`⏩ 加速模式: ${multiplier}x (快速预览)`);
  } else {
    console.log(`⏸️ 正常速度: ${multiplier}x (观察细节)`);
  }

  // 如果正在执行，显示速度变化提示
  if (pathExecutionSystem.value.isRunning) {
    console.log(`🎯 当前执行速度: ${multiplier}x (实时生效)`);

    // 高倍速警告
    if (multiplier >= 50) {
      console.warn(`⚠️ 高倍速模式 (${multiplier}x)，建议关注整体效果而非细节`);
    }
  }
};

// 设置终点（优化版）
const setEndPoint = () => {
  if (mapInteraction.value.clickedPoint) {
    mapInteraction.value.endPoint = { ...mapInteraction.value.clickedPoint };
    console.log('🏁 终点已设置:', mapInteraction.value.endPoint);

    // 添加红色终点旗帜
    nextTick(() => {
      addEndFlag(mapInteraction.value.endPoint);
    });
  }
  hideMapContextMenu();
};

// 隐藏地图右键菜单
const hideMapContextMenu = () => {
  mapInteraction.value.showContextMenu = false;
  mapInteraction.value.clickedPoint = null;
};

// 处理调度请求坐标设置
const handleCoordinateRequest = (coordinateType, processType) => {
  if (coordinateType === 'process-type-changed') {
    // 工艺类型变化，更新当前工艺类型并清空相关小旗
    scheduleRequest.value.currentProcessType = processType;
    clearScheduleFlags();
    console.log(`🔄 工艺类型变更为: ${getProcessTypeName(processType)}`);
  } else if (coordinateType === 'clear-flag') {
    // 清除指定类型的小旗
    removeScheduleFlag(processType);
    console.log(`🗑️ 清除小旗: ${getCoordinateTypeName(processType)}`);
  } else {
    scheduleRequest.value.isWaitingForCoordinate = true;
    scheduleRequest.value.coordinateType = coordinateType;
    scheduleRequest.value.processType = processType;
    console.log(`📍 等待设置调度坐标: ${coordinateType}, 工艺类型: ${processType}`);
  }
};

// 设置调度坐标
const setScheduleCoordinate = (coordinateType) => {
  if (mapInteraction.value.clickedPoint) {
    const coordinate = {
      x: mapInteraction.value.clickedPoint.x,
      z: mapInteraction.value.clickedPoint.z
    };

    // 通过右侧面板设置坐标，传递坐标类型和坐标数据
    if (rightPartRef.value) {
      rightPartRef.value.setScheduleCoordinate(coordinateType, coordinate);
    }

    // 创建调度坐标小旗
    addScheduleFlag(coordinate, coordinateType);

    console.log(`✅ 调度坐标已设置: ${getCoordinateTypeName(coordinateType)}`, coordinate);
  }
  hideMapContextMenu();
};

// 处理调度请求提交
const handleScheduleSubmit = (request) => {
  console.log('📋 收到调度请求:', request);

  // 如果包含路线数据，则渲染路线
  if (request.routeData && routeRenderer.value) {
    console.log('🛣️ 开始渲染导航路线:', request.routeData);
    routeRenderer.value.parseAndCreateRoutes(request.routeData);
  }

  // 这里可以添加提交到后端的逻辑
  // 例如：await submitScheduleRequest(request);

  alert(`调度请求已提交成功！\n工艺类型: ${getProcessTypeName(request.processType)}\n布料方向: ${getFabricDirectionName(request.fabricDirection)}\n已生成 ${request.routeData ? request.routeData.length : 0} 条导航路线`);
};

// 获取工艺类型名称
const getProcessTypeName = (type) => {
  const names = { 1: '转储', 2: '装船', 3: '卸船分料' };
  return names[type] || '未知';
};

// 获取布料方向名称
const getFabricDirectionName = (direction) => {
  const names = { 0: '从左往右', 1: '从右往左' };
  return names[direction] || '未知';
};

// 处理路线显示状态变化
const handleRouteVisibilityChanged = (data) => {
  console.log('🛣️ 路线显示状态变化:', data);

  if (routeRenderer.value) {
    // 根据模型ID查找对应的设备ID
    // 这里需要建立模型ID到设备ID的映射关系
    // 暂时使用简单的映射逻辑，实际项目中应该根据业务逻辑来映射
    const deviceIdMap = {
      // 可以根据实际情况建立映射关系
      // 例如：model_12345 -> device_26482
    };

    // 尝试直接使用modelId作为deviceId，或者使用映射
    let deviceId = deviceIdMap[data.modelId] || data.modelId;

    // 获取所有可用的设备ID
    const availableDeviceIds = routeRenderer.value.getDeviceIds();

    // 只有当设备ID在可用路线列表中时，才允许操作
    if (availableDeviceIds.includes(deviceId)) {
      if (data.visible) {
        routeRenderer.value.showRoute(deviceId);
        console.log(`✅ 显示设备 ${deviceId} 的路线`);
      } else {
        routeRenderer.value.hideRoute(deviceId);
        console.log(`✅ 隐藏设备 ${deviceId} 的路线`);
      }
    } else {
      console.warn(`⚠️ 设备 ${data.modelId} (映射为 ${deviceId}) 没有对应的导航路线，无法操作路线显示状态`);
      console.log(`📋 可用的设备路线列表:`, availableDeviceIds);
    }
  }
};

// 获取坐标类型名称
const getCoordinateTypeName = (coordinateType) => {
  const names = {
    'fabricStartPoint': '布料起点',
    'fabricEndPoint': '布料终点',
    'pickupStartPoint': '取料起点',
    'pickupEndPoint': '取料终点',
    'unloadPickupPoint': '取料点',
    'firstTeamFabricStart': '第一队布料起点',
    'firstTeamFabricEnd': '第一队布料终点',
    'secondTeamFabricStart': '第二队布料起点',
    'secondTeamFabricEnd': '第二队布料终点',
    'shipPickupStart': '取料起点',
    'shipPickupEnd': '取料终点',
    'shipFabricEnd': '布料终点'
  };
  return names[coordinateType] || '坐标点';
};

// 创建调度坐标小旗
const addScheduleFlag = (position, coordinateType) => {
  if (!sceneManagerRef.value) return;

  const scene = sceneManagerRef.value.getScene();
  if (!scene) return;

  // 移除现有的同类型小旗
  removeScheduleFlag(coordinateType);

  // 根据坐标类型选择颜色
  const flagColor = getScheduleFlagColor(coordinateType);

  // 创建调度小旗（使用专门的函数）
  const flag = createScheduleFlag(position, flagColor, coordinateType);
  scene.add(flag);

  // 存储小旗引用
  scheduleRequest.value.flags.set(coordinateType, flag);

  console.log(`🚩 调度小旗已添加: ${getCoordinateTypeName(coordinateType)}`, position);
};

// 移除指定类型的调度小旗
const removeScheduleFlag = (coordinateType) => {
  if (!sceneManagerRef.value) return;

  const scene = sceneManagerRef.value.getScene();
  if (!scene) return;

  const flag = scheduleRequest.value.flags.get(coordinateType);
  if (flag) {
    scene.remove(flag);
    scheduleRequest.value.flags.delete(coordinateType);
    console.log(`🗑️ 调度小旗已移除: ${getCoordinateTypeName(coordinateType)}`);
  }
};

// 清空所有调度小旗
const clearScheduleFlags = () => {
  if (!sceneManagerRef.value) return;

  const scene = sceneManagerRef.value.getScene();
  if (!scene) return;

  scheduleRequest.value.flags.forEach((flag, coordinateType) => {
    scene.remove(flag);
    console.log(`🗑️ 清除调度小旗: ${getCoordinateTypeName(coordinateType)}`);
  });

  scheduleRequest.value.flags.clear();
  console.log('🧹 所有调度小旗已清空');
};

// 获取调度小旗颜色
const getScheduleFlagColor = (coordinateType) => {
  // 根据坐标类型返回不同颜色
  const colorMap = {
    // 转储工艺 - 蓝色系
    'fabricStartPoint': 0x00bfff,    // 深天蓝 - 布料起点
    'fabricEndPoint': 0x1e90ff,     // 道奇蓝 - 布料终点
    'pickupStartPoint': 0x4169e1,   // 皇家蓝 - 取料起点
    'pickupEndPoint': 0x0000ff,     // 纯蓝 - 取料终点

    // 装船工艺 - 橙色系
    'shipPickupStart': 0xff8c00,    // 深橙 - 取料起点
    'shipPickupEnd': 0xff6347,      // 番茄红 - 取料终点
    'shipFabricEnd': 0xff4500,      // 橙红 - 布料终点

    // 卸船分料工艺 - 紫色系
    'unloadPickupPoint': 0x6a0dad,     // 深紫 - 取料点
    'firstTeamFabricStart': 0x9370db,  // 中紫 - 第一队布料起点
    'firstTeamFabricEnd': 0x8a2be2,    // 蓝紫 - 第一队布料终点
    'secondTeamFabricStart': 0x9932cc, // 深兰花紫 - 第二队布料起点
    'secondTeamFabricEnd': 0x8b00ff    // 紫罗兰 - 第二队布料终点
  };

  return colorMap[coordinateType] || 0xffffff; // 默认白色
};

// 获取当前工艺类型的可用坐标字段
const getAvailableCoordinateFields = (processType) => {
  const fields = [];

  switch (processType) {
    case 1: // 转储
      fields.push(
        { key: 'fabricStartPoint', label: '布料起点' },
        { key: 'fabricEndPoint', label: '布料终点' },
        { key: 'pickupStartPoint', label: '取料起点' },
        { key: 'pickupEndPoint', label: '取料终点' }
      );
      break;
    case 2: // 装船
      fields.push(
        { key: 'shipPickupStart', label: '取料起点' },
        { key: 'shipPickupEnd', label: '取料终点' },
        { key: 'shipFabricEnd', label: '布料终点' }
      );
      break;
    case 3: // 卸船分料
      fields.push(
        { key: 'unloadPickupPoint', label: '取料点' },
        { key: 'firstTeamFabricStart', label: '第一队布料起点' },
        { key: 'firstTeamFabricEnd', label: '第一队布料终点' },
        { key: 'secondTeamFabricStart', label: '第二队布料起点' },
        { key: 'secondTeamFabricEnd', label: '第二队布料终点' }
      );
      break;
  }

  return fields;
};

// 生成设备类型映射
const getDeviceTypeCode = (model) => {
  // 优先使用模型的deviceType字段（如果存在）
  if (model.deviceType) {
    // 将deviceType ID映射为数字代码
    const deviceTypeIdMap = {
      'fan_spreader': '0',      // 扇形布料机
      'bucket_wheel_1500': '1', // 1500T斗轮挖掘机
      'bucket_wheel_3500': '1', // 3500T斗轮挖掘机
      'bridge_loader': '2',     // 桥式转载机
      'relay_loader': '3',      // 中继转载机
      'mobile_loader': '4',     // 移动转载机
      'large_spreader': '0'     // 大型布料机（归类为扇形布料机）
    };

    const typeCode = deviceTypeIdMap[model.deviceType];
    if (typeCode) {
      console.log(`设备 ${model.id} 类型: ${model.deviceType} -> ${typeCode}`);
      return typeCode;
    }
  }

  // 备用方案：使用模型路径映射
  if (model.path) {
    const pathTypeMap = {
      '/models/扇形布料机ok.glb': '0',
      '/models/new/扇形布料机ok.glb': '0',
      '/models/1500T斗轮挖掘机.glb': '1',
      '/models/new/1500T斗轮挖掘机.glb': '1',
      '/models/3500T斗轮挖掘机.glb': '1',
      '/models/new/3500T斗轮挖掘机.glb': '1',
      '/models/桥式转载机ok.glb': '2',
      '/models/new/桥式转载机ok.glb': '2',
      '/models/中继转载机-B-ok.glb': '3',
      '/models/new/中继转载机-B-ok.glb': '3',
      '/models/移动转载机ok.glb': '4',
      '/models/new/移动转载机ok.glb': '4',
      '/models/大型布料机ok-6.glb': '0',
      '/models/new/大型布料机ok-6.glb': '0'
    };

    const typeCode = pathTypeMap[model.path];
    if (typeCode) {
      console.log(`设备 ${model.id} 路径: ${model.path} -> ${typeCode}`);
      return typeCode;
    }
  }

  // 最后尝试从模型名称推断
  if (model.name) {
    const nameTypeMap = {
      '扇形布料机': '0',
      '大型布料机': '0',
      '1500T斗轮挖掘机': '1',
      '3500T斗轮挖掘机': '1',
      '桥式转载机': '2',
      '中继转载机': '3',
      '移动转载机': '4'
    };

    for (const [name, code] of Object.entries(nameTypeMap)) {
      if (model.name.includes(name)) {
        console.log(`设备 ${model.id} 名称: ${model.name} -> ${code}`);
        return code;
      }
    }
  }

  console.warn(`无法确定设备类型: ${JSON.stringify({
    id: model.id,
    name: model.name,
    deviceType: model.deviceType,
    path: model.path
  })}，使用默认设备类型 0`);

  return '0'; // 默认为扇形布料机
};

// 生成请求数据
const generateRequestData = (targetPoint) => {
  console.log('生成请求数据，目标点:', targetPoint);
  console.log('当前模型数量:', models.value.length);

  const garage_device_list = models.value
    .filter(model => {
      const isValid = validateModelData(model);
      if (!isValid) {
        console.warn(`跳过无效模型: ${model?.id || '未知ID'}`);
      }
      return isValid;
    })
    .map(model => {
      console.log('处理模型:', {
        id: model.id,
        name: model.name,
        deviceType: model.deviceType,
        path: model.path
      });

      // 获取位置信息，提供默认值
      const position = {
        x: model.currentPosition?.x ?? model.position?.x ?? 0,
        y: 0, // 固定为0
        z: model.currentPosition?.z ?? model.position?.z ?? 0
      };

      // 获取旋转信息，提供默认值
      const rotation = {
        x: model.currentRotation?.x ?? model.rotation?.x ?? 0,
        y: model.currentRotation?.y ?? model.rotation?.y ?? 0,
        z: model.currentRotation?.z ?? model.rotation?.z ?? 0
      };

      const deviceData = {
        device_id: model.id,
        device_position: position,
        device_rotation: rotation,
        device_type: getDeviceTypeCode(model) // 传入完整的model对象
      };

      console.log('设备数据:', deviceData);
      return deviceData;
    });

  const requestData = {
    garage_device_list,
    target_point: targetPoint,
    target_device_type: selectedTargetVehicleType.value, // 使用选中的车型
    flag_visualize:0
  };

  console.log('完整请求数据:', requestData);
  return requestData;
};

// 能耗仿真
const performEnergySimulation = async () => {
  if (!mapInteraction.value.endPoint) {
    alert('请先右击地图设置终点');
    return;
  }

  if (models.value.length === 0) {
    alert('场景中没有设备，请先添加设备');
    return;
  }

  try {
    // 设置仿真状态
    mapInteraction.value.isSimulating = true;

    console.log('🚀 开始能耗仿真...');
    console.log('终点坐标:', mapInteraction.value.endPoint);
    console.log('场景设备数量:', models.value.length);

    // 验证终点数据
    if (!mapInteraction.value.endPoint.x && mapInteraction.value.endPoint.x !== 0) {
      throw new Error('终点坐标无效：缺少x坐标');
    }
    if (!mapInteraction.value.endPoint.z && mapInteraction.value.endPoint.z !== 0) {
      throw new Error('终点坐标无效：缺少z坐标');
    }

    // 生成请求数据
    const requestData = generateRequestData(mapInteraction.value.endPoint);
    console.log('请求数据生成完成:', requestData);

    // 验证请求数据
    if (!requestData.garage_device_list || requestData.garage_device_list.length === 0) {
      throw new Error('没有有效的设备数据，请检查场景中的设备');
    }

    if (!requestData.target_point) {
      throw new Error('目标点数据无效');
    }

    console.log(`准备发送仿真请求，包含 ${requestData.garage_device_list.length} 台设备`);

    // 首先尝试调用真实的HTTP接口
    let response = null;
    try {
      response = await callRealEnergyAPI(requestData);
      console.log('真实接口调用成功，响应数据:', response);
    } catch (apiError) {
      console.warn('真实接口调用失败，使用模拟数据:', apiError.message);
      // 如果真实接口失败，使用模拟数据
      response = await simulateEnergyAPI(requestData);
      console.log('使用模拟数据响应:', response);
    }

    if (response && response.length > 0) {
      console.log('✅ 能耗仿真成功，响应数据:', response);

      // 验证响应数据的完整性
      const validDevices = response.filter(deviceData => {
        if (!deviceData.device_id) {
          console.warn('跳过无效设备数据（缺少device_id）:', deviceData);
          return false;
        }
        if (!deviceData.path || deviceData.path.length === 0) {
          console.warn(`跳过设备 ${deviceData.device_id}（无路径数据）`);
          return false;
        }
        return true;
      });

      // if (validDevices.length === 0) {
      //   throw new Error('响应数据中没有有效的设备路径');
      // }

      console.log(`有效设备数量: ${validDevices.length}/${response.length}`);

      // 更新路径数据
      pathExecutionSystem.value.pathData = validDevices;

      // 重新初始化车辆状态（保持车辆原有电量）
      pathExecutionSystem.value.vehicleStates.clear();
      validDevices.forEach(vehicleData => {
        const model = models.value.find(m => m.id === vehicleData.device_id);
        // 保持车辆当前的电量作为起始电量
        const currentPower = model?.battery || 100;

        pathExecutionSystem.value.vehicleStates.set(vehicleData.device_id, {
          currentPathIndex: 0,
          isMoving: false,
          hasStarted: false,
          currentPosition: null,
          currentRotation: null,
          targetPosition: null,
          targetRotation: null,
          moveStartTime: 0,
          moveDuration: 0,
          currentPower: currentPower,
          startPower: currentPower, // 记录车辆加载时的电量
          initialPower: currentPower, // 记录当前路径点开始时的电量
          currentConsume: model?.consume || 0, // 当前累计耗电量
          initialConsume: model?.consume || 0, // 当前路径点开始时的累计耗电量
          lastLogTime: 0 // 用于控制日志输出频率
        });

        console.log(`⚡ 车辆 ${vehicleData.device_id} 准备仿真: 当前电量 ${currentPower}%`);
      });

      console.log('🚀 路径数据已更新，开始执行移动');

      // 自动开始执行路径
      startPathExecution();

      // 成功提示
      console.log(`✅ 能耗仿真完成，${validDevices.length} 台设备开始移动`);

    } else {
      console.error('能耗仿真返回空数据');
      throw new Error('API返回空数据或无效数据');
    }

  } catch (error) {
    console.error('能耗仿真失败:', error);

    // 提供更友好的错误提示
    let errorMessage = '能耗仿真失败：';
    let isNetworkError = false;

    if (error.message.includes('网络连接失败')) {
      errorMessage += '无法连接到仿真服务器，请检查：\n1. 服务器是否启动 (localhost:8085)\n2. 网络连接是否正常\n3. 防火墙设置是否阻止连接';
      isNetworkError = true;
    } else if (error.message.includes('API服务错误')) {
      errorMessage += '仿真服务器返回错误，请检查服务器状态';
      isNetworkError = true;
    } else if (error.message.includes('API响应格式错误')) {
      errorMessage += '服务器响应数据格式不正确，请联系技术支持';
    } else if (error.message.includes('缺少x坐标') || error.message.includes('缺少z坐标')) {
      errorMessage += '终点坐标无效，请重新右击地图设置终点';
    } else if (error.message.includes('没有有效的设备数据')) {
      errorMessage += '场景中没有有效的设备，请先添加设备到场景中';
    } else if (error.message.includes('目标点数据无效')) {
      errorMessage += '目标点数据无效，请重新设置终点';
    } else if (error.message.includes('API返回空数据')) {
      errorMessage += '服务器返回空数据，请检查请求参数或联系技术支持';
    } else {
      errorMessage += error.message || '未知错误';
    }

    // 如果是网络错误，提供额外的提示
    if (isNetworkError) {
      errorMessage += '\n\n💡 提示：系统将自动使用模拟数据继续演示';
      console.log('💡 由于网络错误，建议检查API服务状态');
    }

    // alert(errorMessage);

    // 提供详细的调试信息
    console.log('🔍 调试信息:');
    console.log('- 错误类型:', error.name);
    console.log('- 错误消息:', error.message);
    console.log('- 终点坐标:', mapInteraction.value.endPoint);
    console.log('- 场景设备数量:', models.value.length);
    console.log('- API地址:', 'http://localhost:8085/mockEnergy');

    if (error.message.includes('设备数据') || error.message.includes('坐标')) {
      console.log('- 设备列表:', models.value.map(m => ({
        id: m.id,
        position: m.position,
        currentPosition: m.currentPosition
      })));
    }
  } finally {
    // 重置仿真状态
    mapInteraction.value.isSimulating = false;
  }
};

// 调用真实的能耗仿真API
const callRealEnergyAPI = async (requestData) => {
  const apiUrl = 'http://localhost:5001/cal_power_consumption';

  console.log('调用真实API:', apiUrl);
  console.log('请求数据:', JSON.stringify(requestData, null, 2));

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestData)
    });

    console.log('HTTP响应状态:', response.status, response.statusText);

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();
    console.log('API响应数据:', responseData);

    // 验证响应数据格式
    if (!Array.isArray(responseData)) {
      throw new Error('API响应格式错误：期望数组格式');
    }

    // 验证每个设备数据的格式
    for (const deviceData of responseData) {
      if (!deviceData.device_id || !deviceData.path || !Array.isArray(deviceData.path)) {
        throw new Error(`设备数据格式错误: ${JSON.stringify(deviceData)}`);
      }
    }

    return responseData;

  } catch (error) {
    console.error('真实API调用失败:', error);

    // 提供更详细的错误信息
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('网络连接失败，请检查API服务是否启动');
    } else if (error.message.includes('HTTP错误')) {
      throw new Error(`API服务错误: ${error.message}`);
    } else {
      throw error;
    }
  }
};

// 模拟能耗API调用（备用方案）
const simulateEnergyAPI = async (requestData) => {
  console.log('使用模拟数据进行能耗仿真...');

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 模拟返回path.json格式的数据
  const simulatedResponse = requestData.garage_device_list.map((device, index) => {
    const finalPower = Math.floor(Math.random() * 30) + 20; // 20-50%电量
    const initialPower = 100; // 假设初始电量为100%
    const powerConsumption = initialPower - finalPower; // 电量消耗
    const cumulativeConsume = Math.floor(Math.random() * 50) + powerConsumption; // 累计耗电量

    return {
      device_id: device.device_id,
      start_time: index * 2, // 错峰开始
      consume: cumulativeConsume, // 总累计耗电量
      path: [
        {
          position: requestData.target_point,
          rotation: device.device_rotation,
          duration: 3000 + Math.random() * 2000, // 3-5秒移动时间
          power: finalPower, // 最终电量
          consume: cumulativeConsume // 到达此点的累计耗电量
        }
      ]
    };
  });

  console.log('模拟响应数据:', simulatedResponse);
  return simulatedResponse;
};

// 显示右键菜单
const showContextMenu = (modelId, x, y) => {
  // 先隐藏之前的菜单，清理事件监听器
  hideContextMenu();

  contextMenu.value.visible = true;
  contextMenu.value.x = x;
  contextMenu.value.y = y;
  contextMenu.value.targetModelId = modelId;

  // 添加点击其他地方关闭菜单的监听
  setTimeout(() => {
    document.addEventListener('click', hideContextMenu, { once: true });
  }, 0);
};

// 隐藏右键菜单
const hideContextMenu = () => {
  contextMenu.value.visible = false;
  contextMenu.value.targetModelId = null;
  document.removeEventListener('click', hideContextMenu);
};

// 菜单项：进入第一视角
const enterFirstPersonView = () => {
  if (contextMenu.value.targetModelId) {
    toggleFirstPersonView(contextMenu.value.targetModelId);
  }
  hideContextMenu();
};

// 菜单项：选择模型（用于移动控制）
const selectModelForMovement = () => {
  if (contextMenu.value.targetModelId) {
    movementControls.value.selectedModelId = contextMenu.value.targetModelId;
    console.log(`Selected model for movement: ${contextMenu.value.targetModelId}`);
  }
  hideContextMenu();
};

// 获取模型名称
const getModelName = (modelId) => {
  if (!modelId) return '';
  const model = models.value.find(m => m.id === modelId);
  return model ? model.name : modelId;
};

// 更新模型的初始坐标
const updateModelInitialPosition = (modelId, position) => {
  const model = models.value.find(m => m.id === modelId);
  if (model) {
    model.initialPosition = { ...position };
    console.log(`Updated initial position for model ${modelId}:`, position);
  }
};

// 更新模型的初始角度
const updateModelInitialRotation = (modelId, rotation) => {
  const model = models.value.find(m => m.id === modelId);
  if (model) {
    model.initialRotation = { ...rotation };
    console.log(`Updated initial rotation for model ${modelId}:`, rotation);
  }
};

// 重置模型到初始位置和角度
const resetModelToInitial = modelId => {
  if (sceneManagerRef.value) {
    const modelRef = sceneManagerRef.value.modelRefs?.[modelId];
    if (modelRef && modelRef.resetToInitialTransform) {
      modelRef.resetToInitialTransform();
      // 同时更新currentPosition和currentRotation
      const model = models.value.find(m => m.id === modelId);
      if (model) {
        model.currentPosition = { ...model.initialPosition };
        model.currentRotation = { ...model.initialRotation };
      }
      console.log(`Model ${modelId} reset to initial transform`);
    }
  }
};

// 将当前位置设为初始位置
const setCurrentAsInitial = modelId => {
  const model = models.value.find(m => m.id === modelId);
  if (model && sceneManagerRef.value) {
    const modelRef = sceneManagerRef.value.modelRefs?.[modelId];
    if (modelRef) {
      // 获取当前位置和角度
      const currentPos = modelRef.getCurrentPosition();
      const currentRot = modelRef.getCurrentRotation();

      if (currentPos && currentRot) {
        // 更新初始坐标
        model.initialPosition = { ...currentPos };
        model.initialRotation = { ...currentRot };
        console.log(`Set current position as initial for model ${modelId}:`, currentPos, currentRot);
      }
    }
  }
};

// 组件卸载时移除键盘事件监听
onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeyboardMovement);
  // 清理第一视角资源
  if (firstPersonView.value.enabled) {
    disableFirstPersonView();
  }
  // 清理右键菜单事件监听
  document.removeEventListener('click', hideContextMenu);
  // 清理卡片位置更新定时器
  stopCardPositionUpdates();
  // 清理模型位置同步定时器
  stopModelPositionSync();

  // 停止路径执行
  stopPathExecution();

  // 清理地图右键事件监听
  const sceneContainer = document.querySelector('.three-container');
  if (sceneContainer) {
    sceneContainer.removeEventListener('contextmenu', handleMapRightClick, { capture: true });
    sceneContainer.removeEventListener('click', hideMapContextMenu);
  }

  // 清理防抖定时器
  if (rightClickDebounceTimer) {
    clearTimeout(rightClickDebounceTimer);
    rightClickDebounceTimer = null;
  }

  // 清理调度小旗
  clearScheduleFlags();

  // 清理旗帜
  removeStartFlag();
  removeEndFlag();
  // 清理全局场景管理器引用
  if (window.sceneManagerRef) {
    delete window.sceneManagerRef;
    console.log('Global sceneManagerRef cleaned up');
  }
});

const currentTime = ref('');

// 添加时间更新函数
const updateTime = () => {
  const now = new Date();
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  });
};

// 组件挂载时开始更新时间
onMounted(() => {
  updateTime();
  setInterval(updateTime, 1000);
});

// 组件挂载时启动报警检查
onMounted(() => {
  // 启动报警检查定时器 (每30秒检查一次电量)
  const alertCheckInterval = setInterval(() => {
    checkBatteryAlerts();
  }, 30000);

  // 组件卸载时清除定时器
  onBeforeUnmount(() => {
    clearInterval(alertCheckInterval);
  });

  // 初始检查一次电量
  checkBatteryAlerts();

  // 添加一些示例预期路径用于偏移检测
  expectedPaths.value.set('car1', { x: -80, z: -60 });
  expectedPaths.value.set('car3', { x: 120, z: -90 });
  expectedPaths.value.set('car7', { x: 675, z: -496 });

  // 设置全局场景管理器引用，供 RouteSimulator 使用
  window.sceneManagerRef = sceneManagerRef.value;
  console.log('Global sceneManagerRef set for RouteSimulator');

  // 初始化WebSocket RouteSimulator
  initializeWebSocketSimulator();
});

// WebSocket RouteSimulator 相关
const websocketSimulator = ref(null);
const websocketConnected = ref(false);

// 初始化WebSocket RouteSimulator
const initializeWebSocketSimulator = () => {
  console.log('🔗 初始化WebSocket RouteSimulator...');

  websocketSimulator.value = new RouteSimulator({
    interpolationMode: 'smooth',
    speed: 1.0,
    loop: false,
    smoothRotation: true,
    enableSmoothAnimation: true // 启用流畅动画控制器
  });

  // 设置模型列表引用
  websocketSimulator.value.allModels = models.value;

  // 设置场景管理器引用
  websocketSimulator.value.sceneManager = sceneManagerRef.value;

  // 设置位置更新回调
  websocketSimulator.value.onPositionUpdate = (updateData) => {
    // 通知场景管理器更新模型位置
    if (sceneManagerRef.value && sceneManagerRef.value.updateModelPosition) {
      sceneManagerRef.value.updateModelPosition(
        updateData.device_id,
        updateData.position,
        updateData.rotation
      );
    }

    // 更新模型数据
    const model = models.value.find(m => m.id === updateData.device_id);
    if (model) {
      model.currentPosition = updateData.position;
      model.currentRotation = updateData.rotation;
      if (typeof updateData.battery === 'number') {
        model.battery = updateData.battery;
      }
      if (typeof updateData.consume === 'number') {
        model.consume = updateData.consume;
      }
      if (typeof updateData.distance === 'number') {
        model.distance = updateData.distance;
      }
    }
  };

  // 连接WebSocket
  connectWebSocketSimulator();

  // 暴露测试函数
  exposeTestFunctions();
};

// 连接WebSocket
const connectWebSocketSimulator = () => {
  const websocketUrl = 'ws://localhost:8080/route';
  console.log('🔗 连接WebSocket:', websocketUrl);

  if (websocketSimulator.value.connectWebSocket(websocketUrl)) {
    websocketConnected.value = true;
    console.log('✅ WebSocket连接成功');
  } else {
    websocketConnected.value = false;
    console.error('WebSocket连接失败');
  }
};

// 暴露测试函数到全局，方便调试
const exposeTestFunctions = () => {
  if (websocketSimulator.value) {
    window.testEmptyArray = () => websocketSimulator.value.testEmptyArrayHandling();
    window.testYCoordinate = () => websocketSimulator.value.testYCoordinateFix();
    window.testYDisplay = () => websocketSimulator.value.testYCoordinateShaking();
    window.testYValues = () => websocketSimulator.value.testDifferentYCoordinates();
    window.testDelayQueue = () => websocketSimulator.value.testUniformMotion();
    window.testSmoothAnimation = () => websocketSimulator.value.testQueueBuffer();
    window.testDataFlow = () => websocketSimulator.value.testDataFlow();
    window.getSmoothAnimationStatus = () => websocketSimulator.value.getSmoothAnimationStatus();
    window.stopAllSmoothAnimations = () => websocketSimulator.value.smoothAnimationController?.stopAllAnimations();

    console.log('测试函数已暴露到全局:');
    console.log('  - window.testEmptyArray() - 测试空数组处理');
    console.log('  - window.testYCoordinate() - 测试Y坐标修正');
    console.log('  - window.testYDisplay() - 测试Y坐标显示');
    console.log('  - window.testYValues() - 测试不同Y坐标值');
    console.log('  - window.testDelayQueue() - 测试延时队列');
    console.log('  - window.testSmoothAnimation() - 测试流畅动画');
    console.log('  - window.testDataFlow() - 测试数据流调试');
    console.log('  - window.getSmoothAnimationStatus() - 获取动画状态');
    console.log('  - window.stopAllSmoothAnimations() - 停止所有动画');
  }
};

// 地图加载相关
const currentMap = ref(null);

// 处理地图开始加载
const handleMapLoading = (mapData) => {
  console.log('🗺️ 接收到地图加载开始信号:', mapData.name);
  console.log('📋 地图配置预览:', {
    id: mapData.id,
    name: mapData.name,
    path: mapData.path,
    hasPosition: !!mapData.position,
    hasRotation: !!mapData.rotation,
    hasScale: !!mapData.scale
  });

  // 验证地图路径
  if (!mapData.path) {
    console.error('地图路径为空，无法加载地图');
    alert('地图路径无效，请检查地图配置');
    return;
  }

  // 可以在这里添加加载状态指示
  if (sceneManagerRef.value) {
    console.log('🧹 准备清除当前地图...');
    // 清除当前场景中的地图模型
    clearCurrentMap();
  } else {
    console.warn('⚠️ 场景管理器未初始化，无法清除当前地图');
  }
};



// 清除当前地图
const clearCurrentMap = () => {
  if (!sceneManagerRef.value) {
    console.warn('⚠️ 场景管理器未初始化，无法清除地图');
    return;
  }

  const scene = sceneManagerRef.value.getScene();
  if (!scene) {
    console.warn('⚠️ 场景未初始化，无法清除地图');
    return;
  }

  // 查找并移除地图模型
  const mapObjects = scene.children.filter(child =>
    child.userData && child.userData.type === 'map'
  );

  console.log(`🔍 找到 ${mapObjects.length} 个地图对象需要清除`);

  mapObjects.forEach((mapObject, index) => {
    console.log(`🗑️ 清除地图对象 ${index + 1}: ${mapObject.userData.name || 'Unknown'}`);

    scene.remove(mapObject);

    // 清理几何体和材质
    if (mapObject.traverse) {
      mapObject.traverse((child) => {
        if (child.geometry) {
          child.geometry.dispose();
        }
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(material => {
              if (material.map) material.map.dispose();
              material.dispose();
            });
          } else {
            if (child.material.map) child.material.map.dispose();
            child.material.dispose();
          }
        }
      });
    }
  });

  // 清除当前地图引用
  currentMap.value = null;

  console.log(`✅ 已清除 ${mapObjects.length} 个地图模型`);
};

// 加载地图模型
const loadMapModel = async (mapData) => {
  console.log('🔧 loadMapModel 开始执行');
  console.log('📋 接收到的地图数据:', mapData);

  if (!sceneManagerRef.value || !mapData.path) {
    const error = '场景管理器未初始化或地图路径无效';
    console.error('❌', error);
    throw new Error(error);
  }

  const scene = sceneManagerRef.value.getScene();
  if (!scene) {
    const error = '场景未初始化';
    console.error('❌', error);
    throw new Error(error);
  }

  console.log('✅ 场景管理器和场景都已就绪');
  console.log('📁 准备加载地图文件:', mapData.path);

  return new Promise((resolve, reject) => {
    const loader = new GLTFLoader();

    loader.load(
      mapData.path,
      (gltf) => {
        const mapModel = gltf.scene;

        // 设置地图模型属性
        mapModel.userData = {
          type: 'map',
          id: mapData.id,
          name: mapData.name
        };

        console.log(`🏷️ 地图模型已标记: type='map', id='${mapData.id}', name='${mapData.name}'`);

        // 设置地图位置和缩放
        if (mapData.position) {
          mapModel.position.set(
            mapData.position.x || 0,
            mapData.position.y || 0,
            mapData.position.z || 0
          );
        }

        if (mapData.rotation) {
          mapModel.rotation.set(
            mapData.rotation.x || 0,
            mapData.rotation.y || 0,
            mapData.rotation.z || 0
          );
        }

        if (mapData.scale) {
          mapModel.scale.set(
            mapData.scale.x || 1,
            mapData.scale.y || 1,
            mapData.scale.z || 1
          );
        }

        // 添加到场景
        scene.add(mapModel);

        console.log(`📍 地图模型已添加: ${mapData.name}`);

        // 应用当前的场景材质设置（如果有的话）
        setTimeout(() => {
          if (sceneManagerRef.value && sceneManagerRef.value.updateMapMaterial) {
            // 获取当前的材质设置（可以从场景配置中获取）
            const currentMaterialSettings = {
              metalness: 0.1,  // 默认值，可以从场景配置中获取
              roughness: 0.8   // 默认值，可以从场景配置中获取
            };

            console.log('🎨 应用材质设置到新加载的地图');
            sceneManagerRef.value.updateMapMaterial(currentMaterialSettings);
          }
        }, 100); // 延迟100ms确保地图完全添加到场景

        resolve(mapModel);
      },
      (progress) => {
        const percent = (progress.loaded / progress.total * 100).toFixed(1);
        console.log(`⏳ 地图加载进度: ${percent}%`);
      },
      (error) => {
        console.error('地图模型加载失败:', error);
        reject(error);
      }
    );
  });
};

// 处理感知模拟
const handlePerceptionSimulation = () => {
  console.log('👁️ 开始感知模拟 - 启动障碍物移动');

  // 为所有障碍物加载默认路径并开始移动
  obstacles.value.forEach(obstacle => {
    if (obstacle.defaultPath && obstacle.defaultPath.length > 0) {
      console.log(`🚶 启动障碍物移动: ${obstacle.name}`);

      // 将障碍物的默认路径转换为路径执行系统格式
      const obstaclePathData = {
        device_id: obstacle.id,
        start_time: 0,
        consume: 0,
        path: obstacle.defaultPath
      };

      // 添加到路径执行系统
      if (!pathExecutionSystem.value.pathData.some(p => p.device_id === obstacle.id)) {
        pathExecutionSystem.value.pathData.push(obstaclePathData);

        // 初始化障碍物的车辆状态
        pathExecutionSystem.value.vehicleStates.set(obstacle.id, {
          currentPathIndex: 0,
          isMoving: false,
          hasStarted: false,
          currentPosition: null,
          currentRotation: null,
          targetPosition: null,
          targetRotation: null,
          moveStartTime: 0,
          moveDuration: 0,
          currentPower: obstacle.battery || 100,
          startPower: obstacle.battery || 100,
          initialPower: obstacle.battery || 100,
          currentConsume: obstacle.consume || 0,
          initialConsume: obstacle.consume || 0,
          lastLogTime: 0
        });

        console.log(`✅ 障碍物 ${obstacle.name} 已加入路径执行系统`);
      }
    }
  });

  // 如果路径执行系统没有运行，则启动它
  if (!pathExecutionSystem.value.isRunning && pathExecutionSystem.value.pathData.length > 0) {
    console.log('🚀 启动路径执行系统（包含障碍物）');
    startPathExecution();
  }

  console.log('✅ 感知模拟完成，障碍物开始移动');
};

// 障碍物管理
const obstacles = ref([]);

// 处理障碍物添加
const handleObstacleAdded = async (obstacleData) => {
  console.log('🚶 接收到障碍物添加请求:', obstacleData.name);
  console.log('📋 障碍物配置:', obstacleData);

  try {
    // 加载障碍物模型到场景
    await loadObstacleModel(obstacleData);

    // 添加到障碍物列表
    obstacles.value.push(obstacleData);

    console.log('✅ 障碍物已添加到场景:', obstacleData.name);

  } catch (error) {
    console.error('障碍物添加失败:', error);
    alert('障碍物添加失败：' + error.message);
  }
};

// 处理障碍物删除
const handleObstacleRemoved = (obstacleId) => {
  console.log('🗑️ 删除障碍物:', obstacleId);

  const obstacleIndex = obstacles.value.findIndex(obs => obs.id === obstacleId);
  if (obstacleIndex !== -1) {
    const obstacle = obstacles.value[obstacleIndex];

    // 从场景中移除障碍物模型
    removeObstacleFromScene(obstacleId);

    // 从列表中移除
    obstacles.value.splice(obstacleIndex, 1);

    console.log('✅ 障碍物已从场景删除:', obstacle.name);
  }
};

// 加载障碍物模型到场景
const loadObstacleModel = async (obstacleData) => {
  console.log('🔧 loadObstacleModel 开始执行');
  console.log('📋 接收到的障碍物数据:', obstacleData);

  if (!sceneManagerRef.value || !obstacleData.path) {
    const error = '场景管理器未初始化或障碍物路径无效';
    console.error('❌', error);
    throw new Error(error);
  }

  const scene = sceneManagerRef.value.getScene();
  if (!scene) {
    const error = '场景未初始化';
    console.error('❌', error);
    throw new Error(error);
  }

  console.log('✅ 场景管理器和场景都已就绪');
  console.log('📁 准备加载障碍物文件:', obstacleData.path);

  return new Promise((resolve, reject) => {
    const loader = new GLTFLoader();

    loader.load(
      obstacleData.path,
      (gltf) => {
        const obstacleModel = gltf.scene;

        // 设置障碍物模型属性
        obstacleModel.userData = {
          type: 'obstacle',
          id: obstacleData.id,
          name: obstacleData.name,
          defaultPath: obstacleData.defaultPath
        };

        // 设置障碍物位置、旋转、缩放
        if (obstacleData.position) {
          obstacleModel.position.set(
            obstacleData.position.x,
            obstacleData.position.y,
            obstacleData.position.z
          );
        }

        if (obstacleData.rotation) {
          obstacleModel.rotation.set(
            obstacleData.rotation.x,
            obstacleData.rotation.y,
            obstacleData.rotation.z
          );
        }

        if (obstacleData.scale) {
          obstacleModel.scale.set(
            obstacleData.scale.x,
            obstacleData.scale.y,
            obstacleData.scale.z
          );
        }

        // 设置障碍物的当前位置和旋转（用于移动系统）
        obstacleData.currentPosition = { ...obstacleData.position };
        obstacleData.currentRotation = { ...obstacleData.rotation };

        // 应用颜色设置
        if (obstacleData.color) {
          obstacleModel.traverse((child) => {
            if (child.isMesh && child.material) {
              // 创建新材质以避免影响其他模型
              const newMaterial = child.material.clone();
              newMaterial.color.setHex(obstacleData.color.replace('#', '0x'));
              child.material = newMaterial;
              console.log(`🎨 应用颜色 ${obstacleData.color} 到障碍物材质`);
            }
          });
        }

        // 添加到场景
        scene.add(obstacleModel);

        // 将模型引用存储到障碍物数据中
        obstacleData.model = obstacleModel;

        console.log(`📍 障碍物模型已添加: ${obstacleData.name}`);
        resolve(obstacleModel);
      },
      (progress) => {
        const percent = (progress.loaded / progress.total * 100).toFixed(1);
        console.log(`⏳ 障碍物加载进度: ${percent}%`);
      },
      (error) => {
        console.error('障碍物模型加载失败:', error);
        reject(error);
      }
    );
  });
};

// 从场景中移除障碍物
const removeObstacleFromScene = (obstacleId) => {
  if (!sceneManagerRef.value) return;

  const scene = sceneManagerRef.value.getScene();
  if (!scene) return;

  // 查找并移除障碍物模型
  const obstacleObjects = scene.children.filter(child =>
    child.userData && child.userData.type === 'obstacle' && child.userData.id === obstacleId
  );

  obstacleObjects.forEach(obstacleObject => {
    scene.remove(obstacleObject);

    // 清理几何体和材质
    if (obstacleObject.traverse) {
      obstacleObject.traverse((child) => {
        if (child.geometry) child.geometry.dispose();
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(material => {
              if (material.map) material.map.dispose();
              material.dispose();
            });
          } else {
            if (child.material.map) child.material.map.dispose();
            child.material.dispose();
          }
        }
      });
    }
  });

  console.log('🧹 已清除障碍物模型:', obstacleId);
};

// 测试地图切换功能
const testMapSwitching = () => {
  console.log('🧪 测试地图切换功能...');

  // 检查场景管理器状态
  console.log('场景管理器状态:', {
    sceneManagerRef: !!sceneManagerRef.value,
    scene: !!sceneManagerRef.value?.getScene(),
    currentMap: currentMap.value
  });

  // 检查当前场景中的地图对象
  if (sceneManagerRef.value) {
    const scene = sceneManagerRef.value.getScene();
    if (scene) {
      const mapObjects = scene.children.filter(child =>
        child.userData && child.userData.type === 'map'
      );
      console.log(`当前场景中的地图对象数量: ${mapObjects.length}`);
      mapObjects.forEach((obj, index) => {
        console.log(`地图对象 ${index + 1}:`, {
          name: obj.userData.name,
          id: obj.userData.id,
          position: obj.position,
          visible: obj.visible
        });
      });
    }
  }

  // 手动触发地图切换测试
  const testMapData = {
    id: "test_map",
    name: "测试地图",
    path: "/models/model12.glb",
    position: { x: 0, y: 0, z: 0 },
    rotation: { x: 0, y: 0, z: 0 },
    scale: { x: 5, y: 5, z: 5 }
  };

  console.log('🔄 手动触发地图切换测试...');
  handleMapLoading(testMapData);
  setTimeout(() => {
    handleMapLoaded(testMapData);
  }, 100);
};

// 暴露测试函数到全局
window.testMapSwitching = testMapSwitching;

// 测试地图材质调整功能
const testMapMaterialUpdate = () => {
  console.log('🧪 测试地图材质调整功能...');

  if (!sceneManagerRef.value) {
    console.error('场景管理器未初始化');
    return;
  }

  if (!sceneManagerRef.value.updateMapMaterial) {
    console.error('updateMapMaterial方法不存在');
    return;
  }

  // 测试不同的材质设置
  const testSettings = [
    { metalness: 0.0, roughness: 1.0, name: '完全粗糙' },
    { metalness: 1.0, roughness: 0.0, name: '完全金属' },
    { metalness: 0.5, roughness: 0.5, name: '中等设置' },
    { metalness: 0.1, roughness: 0.8, name: '默认设置' }
  ];

  let currentIndex = 0;

  const applyNextSetting = () => {
    if (currentIndex < testSettings.length) {
      const setting = testSettings[currentIndex];
      console.log(`🎨 应用材质设置 ${currentIndex + 1}: ${setting.name}`, setting);

      sceneManagerRef.value.updateMapMaterial({
        metalness: setting.metalness,
        roughness: setting.roughness
      });

      currentIndex++;
      setTimeout(applyNextSetting, 2000); // 每2秒切换一次
    } else {
      console.log('✅ 地图材质测试完成');
    }
  };

  applyNextSetting();
};

// 暴露材质测试函数到全局
window.testMapMaterialUpdate = testMapMaterialUpdate;

// 更新车辆消耗信息显示
const updateVehicleConsumptionDisplay = (deviceId, pathPoint, vehicleState) => {
  if (!rightPartRef.value || !rightPartRef.value.updateSelectedVehicleConsumption) {
    return;
  }

  // 计算消耗率（基于时间和消耗变化）
  const currentTime = Date.now();
  const timeDelta = vehicleState.lastUpdateTime ? (currentTime - vehicleState.lastUpdateTime) / 1000 : 0.1; // 秒

  // 获取当前累计消耗值（实时线性变化的值）
  const currentTotalConsume = pathPoint.consume || 0;
  const lastTotalConsume = vehicleState.lastConsume || 0;

  // 计算消耗率（kWh/s）
  const consumeDelta = currentTotalConsume - lastTotalConsume;
  const consumeRate = timeDelta > 0 ? Math.abs(consumeDelta) / timeDelta : 0;

  // 更新状态
  vehicleState.lastUpdateTime = currentTime;
  vehicleState.lastConsume = currentTotalConsume;

  const vehicleConsumptionData = {
    deviceId: deviceId,
    currentPower: pathPoint.power || 100,
    totalConsume: currentTotalConsume, // 使用实时线性变化的累计消耗值
    consumeRate: Math.max(0, consumeRate), // 确保消耗率不为负
    lastUpdateTime: currentTime
  };

  // 调用右侧面板的更新方法
  rightPartRef.value.updateSelectedVehicleConsumption(vehicleConsumptionData);

  // 只在调试模式下输出详细日志
  if (Math.random() < 0.01) { // 1%的概率输出日志，避免日志过多
    console.log(`🔋 更新车辆 ${deviceId} 消耗显示: 电量${vehicleConsumptionData.currentPower.toFixed(1)}% 累计消耗${vehicleConsumptionData.totalConsume.toFixed(2)}kWh 消耗率${vehicleConsumptionData.consumeRate.toFixed(3)}kWh/s`);
  }
};

// 测试累计消耗线性变化功能
const testLinearConsumption = () => {
  console.log('🧪 测试累计消耗线性变化功能...');

  // 检查当前执行的车辆状态
  if (pathExecutionSystem.value.vehicleStates.size === 0) {
    console.warn('⚠️ 没有正在执行路径的车辆');
    return;
  }

  pathExecutionSystem.value.vehicleStates.forEach((vehicleState, deviceId) => {
    const model = models.value.find(m => m.id === deviceId);
    if (model) {
      console.log(`🚗 车辆 ${deviceId}:`, {
        currentPower: model.battery?.toFixed(1) + '%',
        currentConsume: model.consume?.toFixed(2) + 'kWh',
        initialPower: vehicleState.initialPower?.toFixed(1) + '%',
        initialConsume: vehicleState.initialConsume?.toFixed(2) + 'kWh',
        isMoving: vehicleState.isMoving,
        currentPathIndex: vehicleState.currentPathIndex
      });
    }
  });

  // 检查右侧面板显示的数据
  if (rightPartRef.value && rightPartRef.value.selectedVehicleConsumption) {
    console.log('📊 右侧面板显示数据:', rightPartRef.value.selectedVehicleConsumption);
  } else {
    console.log('📊 右侧面板暂无显示数据');
  }
};

// 暴露测试函数到全局
window.testLinearConsumption = testLinearConsumption;
</script>

<style scoped>
/* 简洁网格背景 */
.simple-grid-pattern {
  background-image: linear-gradient(rgba(6, 182, 212, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(6, 182, 212, 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
}

/* 标题发光效果 */
.title-glow {
  text-shadow: 0 0 20px rgba(6, 182, 212, 0.5);
  animation: titleGlow 3s ease-in-out infinite;
}

@keyframes titleGlow {
  0%,
  100% {
    text-shadow: 0 0 20px rgba(6, 182, 212, 0.5);
  }
  50% {
    text-shadow: 0 0 30px rgba(6, 182, 212, 0.8), 0 0 40px rgba(34, 211, 238, 0.4);
  }
}

/* 边框扫描动画 */
.animate-scan-horizontal {
  animation: borderScanHorizontal 4s linear infinite;
}

.animate-scan-horizontal-reverse {
  animation: borderScanHorizontalReverse 4s linear infinite;
  animation-delay: 2s;
}

.animate-scan-vertical {
  animation: borderScanVertical 4s linear infinite;
  animation-delay: 1s;
}

.animate-scan-vertical-reverse {
  animation: borderScanVerticalReverse 4s linear infinite;
  animation-delay: 3s;
}

@keyframes borderScanHorizontal {
  0% {
    left: -100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

@keyframes borderScanHorizontalReverse {
  0% {
    right: -100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    right: 100%;
    opacity: 0;
  }
}

@keyframes borderScanVertical {
  0% {
    top: -100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    top: 100%;
    opacity: 0;
  }
}

@keyframes borderScanVerticalReverse {
  0% {
    bottom: -100%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    bottom: 100%;
    opacity: 0;
  }
}

/* 悬停效果 */
.angular-header-container:hover {
  box-shadow: 0 0 60px rgba(6, 182, 212, 0.4), inset 0 0 60px rgba(6, 182, 212, 0.1);
  transform: scale(1.02);
  transition: all 0.3s ease;
}

/* 报警面板样式 */
.alert-panel {
  animation: slideInFromRight 0.3s ease-out;
}

.alert-header {
  position: relative;
  overflow: hidden;
}

.alert-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 2s infinite;
}

.alert-item {
  transition: all 0.2s ease;
}

.alert-item:hover {
  background-color: rgba(30, 41, 59, 0.5) !important;
}

.alert-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(239, 68, 68, 0.3) transparent;
}

.alert-list::-webkit-scrollbar {
  width: 4px;
}

.alert-list::-webkit-scrollbar-track {
  background: transparent;
}

.alert-list::-webkit-scrollbar-thumb {
  background-color: rgba(239, 68, 68, 0.3);
  border-radius: 2px;
}

.alert-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(239, 68, 68, 0.5);
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
</style>
