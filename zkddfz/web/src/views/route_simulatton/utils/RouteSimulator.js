/**
 * 路线模拟器
 * 支持WebSocket实时数据和JSON数组模拟
 */

import * as THREE from 'three';
import { EasingFunctions } from './animationUtils.js';
import { SmoothAnimationController } from './SmoothAnimationController.js';

export class RouteSimulator {
  constructor(options = {}) {
    this.model = null;
    this.modelComponent = null; // ModelComponent引用，用于正确的坐标转换
    this.isRunning = false;
    this.isPaused = false;
    this.currentIndex = 0;
    this.routeData = [];
    this.websocket = null;

    // 配置选项
    this.options = {
      interpolationMode: 'linear', // linear, smooth, bezier
      speed: 1.0, // 仿真速度倍率
      loop: false, // 是否循环仿真
      autoStart: false, // 是否自动开始
      smoothRotation: true, // 是否平滑旋转
      enableSmoothAnimation: true, // 启用流畅动画控制器
      ...options
    };

    // 动画相关
    this.animationId = null;
    this.startTime = 0;
    this.currentAnimation = null;

    // 流畅动画控制器
    this.smoothAnimationController = new SmoothAnimationController({
      bufferSize: 3,
      predictionTime: 1.5,
      smoothingFactor: 0.8,
      maxInterpolationTime: 2.0,
      adaptiveBuffer: true
    });

    // 回调函数
    this.onPositionUpdate = null;
    this.onRouteComplete = null;
    this.onError = null;

    // 设置流畅动画控制器的回调
    this.smoothAnimationController.setPositionUpdateCallback((data) => {
      this.handleSmoothPositionUpdate(data);
    });

    console.log('RouteSimulator initialized with options:', this.options);
  }
  
  /**
   * 设置要控制的模型
   */
  setModel(model) {
    this.model = model;
    console.log('Model set for route simulation');
  }

  /**
   * 设置ModelComponent引用（用于正确的坐标转换）
   */
  setModelComponent(modelComponent) {
    this.modelComponent = modelComponent;
    console.log('ModelComponent set for route simulation');
  }

  /**
   * 设置所有模型的引用（用于WebSocket车辆控制）
   */
  setAllModels(models) {
    this.allModels = models;
    console.log('All models set for WebSocket vehicle control');
  }

  /**
   * 设置场景管理器引用（用于车辆控制）
   */
  setSceneManager(sceneManager) {
    this.sceneManager = sceneManager;
    console.log('Scene manager set for vehicle control');
  }
  
  /**
   * 加载路线数据（JSON数组）
   */
  loadRouteData(data) {
    try {
      this.routeData = this.validateRouteData(data);
      this.currentIndex = 0;
      console.log(`Route data loaded: ${this.routeData.length} waypoints`);
      
      if (this.options.autoStart && this.routeData.length > 0) {
        this.start();
      }
      
      return true;
    } catch (error) {
      console.error('Failed to load route data:', error);
      if (this.onError) this.onError(error);
      return false;
    }
  }
  
  /**
   * 验证路线数据格式
   */
  validateRouteData(data) {
    if (!Array.isArray(data)) {
      throw new Error('Route data must be an array');
    }
    
    return data.map((point, index) => {
      const validated = {
        timestamp: point.timestamp || Date.now() + index * 1000,
        position: {
          x: parseFloat(point.position?.x || point.x || 0),
          y: parseFloat(point.position?.y || point.y || 0),
          z: parseFloat(point.position?.z || point.z || 0)
        },
        rotation: {
          x: parseFloat(point.rotation?.x || point.rotX || 0),
          y: parseFloat(point.rotation?.y || point.rotY || point.yaw || 0),
          z: parseFloat(point.rotation?.z || point.rotZ || 0)
        },
        speed: parseFloat(point.speed || 1.0),
        duration: parseFloat(point.duration || 1000) // 到达此点的持续时间(ms)
      };
      
      return validated;
    });
  }
  
  /**
   * 连接WebSocket进行实时数据接收
   */
  connectWebSocket(url, protocols = []) {
    try {
      // 关闭现有连接
      if (this.websocket) {
        this.websocket.close();
      }

      this.websocket = new WebSocket(url, protocols);

      this.websocket.onopen = () => {
        console.log('WebSocket 连接已建立:', url);

        // 发送连接确认消息
        this.sendWebSocketMessage({
          type: 'connection',
          action: 'hello',
          data: {
            clientType: 'route_simulator',
            timestamp: Date.now()
          }
        });
      };

      this.websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('收到 WebSocket 消息:', message);
          this.handleWebSocketMessage(message);
        } catch (error) {
          console.error('WebSocket 消息解析失败:', error);
          if (this.onError) this.onError(error);
        }
      };

      this.websocket.onerror = (error) => {
        console.error('WebSocket 连接错误:', error);
        if (this.onError) this.onError(error);
      };

      this.websocket.onclose = (event) => {
        console.log('WebSocket 连接已关闭:', event.code, event.reason);
        this.websocket = null;
      };

      return true;
    } catch (error) {
      console.error('WebSocket 连接失败:', error);
      if (this.onError) this.onError(error);
      return false;
    }
  }
  
  /**
   * 处理 WebSocket 消息
   */
  handleWebSocketMessage(message) {
    // 检查是否是车辆控制电文数组格式
    if (Array.isArray(message)) {
      this.handleVehicleControlMessage(message);
      return;
    }

    const { type, action, data } = message;

    switch (type) {
      case 'connection':
        this.handleConnectionMessage(action, data);
        break;
      case 'route_data':
        this.handleRouteDataMessage(action, data);
        break;
      case 'status':
        this.handleStatusMessage(action, data);
        break;
      case 'error':
        this.handleErrorMessage(action, data);
        break;
      default:
        console.warn('未知的 WebSocket 消息类型:', type);
    }
  }

  /**
   * 处理连接消息
   */
  handleConnectionMessage(action, data) {
    switch (action) {
      case 'connected':
        console.log('WebSocket 服务器确认连接:', data);
        break;
      default:
        console.log('连接消息:', action, data);
    }
  }

  /**
   * 处理路线数据消息
   */
  handleRouteDataMessage(action, data) {
    if (!this.model) {
      console.warn('模型未设置，无法处理路线数据');
      return;
    }

    switch (action) {
      case 'point':
        // 单个路线点的实时数据
        this.handleRealtimePoint(data);
        break;
      case 'batch':
        // 批量路线数据
        this.handleBatchRouteData(data);
        break;
      default:
        console.warn('未知的路线数据动作:', action);
    }
  }

  /**
   * 处理状态消息
   */
  handleStatusMessage(action, data) {
    console.log('状态消息:', action, data);
  }

  /**
   * 处理错误消息
   */
  handleErrorMessage(action, data) {
    console.error('服务器错误:', action, data);
    if (this.onError) this.onError(new Error(data));
  }

  /**
   * 处理流畅动画位置更新
   */
  handleSmoothPositionUpdate(data) {
    const { deviceId, position, rotation, velocity, smooth, timestamp, directUpdate, progress } = data;

    // 调试日志
    if (Math.random() < 0.01) { // 1%的概率输出日志
      console.log(`RouteSimulator 接收回调: ${deviceId}, 位置=(${position.x.toFixed(1)}, ${position.z.toFixed(1)}), directUpdate=${directUpdate}`);
    }

    // 查找对应的车辆模型
    if (!this.allModels || !Array.isArray(this.allModels)) {
      console.warn(`allModels 未初始化或不是数组`);
      return;
    }

    const targetModel = this.allModels.find(model => model.id === deviceId);
    if (!targetModel) {
      console.warn(`未找到设备 ${deviceId} 的模型`);
      return;
    }

    // 更新模型数据
    targetModel.currentPosition = position;
    targetModel.currentRotation = rotation;

    // 如果是直接更新模式，直接操作3D模型，跳过ModelComponent的动画
    if (directUpdate && this.sceneManager) {
      this.directUpdateModel3D(deviceId, position, rotation);
    } else {
      // 使用原有的更新方式
      if (this.sceneManager && this.sceneManager.updateModelPosition) {
        this.sceneManager.updateModelPosition(deviceId, position, rotation);
      }
    }

    // 触发位置更新回调（如果存在）
    if (this.onPositionUpdate) {
      this.onPositionUpdate({
        modelId: deviceId,
        device_id: deviceId,
        position: position,
        rotation: rotation,
        velocity: velocity,
        smooth: smooth,
        progress: progress,
        timestamp: timestamp
      });
    }
  }

  /**
   * 处理车辆控制电文
   */
  handleVehicleControlMessage(vehicleCommands) {
    console.log('接收到车辆控制电文:', vehicleCommands);

    if (!Array.isArray(vehicleCommands)) {
      console.warn('车辆控制电文格式错误，期望数组');
      return;
    }

    // 如果接收到空数组，停止所有流畅动画
    if (vehicleCommands.length === 0) {
      console.log('接收到空数组，停止所有车辆动画');
      if (this.options.enableSmoothAnimation && this.smoothAnimationController) {
        this.smoothAnimationController.stopAllAnimations();
        console.log('所有流畅动画已停止');
      }
      return;
    }

    // 如果启用了流畅动画控制器，使用新的处理方式
    if (this.options.enableSmoothAnimation) {
      vehicleCommands.forEach(command => {
        this.processSmoothVehicleCommand(command);
      });
    } else {
      // 使用原有的处理方式
      vehicleCommands.forEach(command => {
        this.executeVehicleCommand(command);
      });
    }
  }

  /**
   * 处理流畅车辆命令（使用流畅动画控制器）
   */
  processSmoothVehicleCommand(command) {
    const { device_id, start_time, position, rotation, duration, power, consume, distance } = command;

    console.log(`处理流畅车辆命令: ${device_id}`, command);

    // 查找对应的车辆模型
    if (!this.allModels || !Array.isArray(this.allModels)) {
      console.warn('车辆模型列表未设置，无法处理命令');
      return;
    }

    const targetModel = this.allModels.find(model => model.id === device_id);
    if (!targetModel) {
      console.warn(`⚠️ 未找到设备ID为 ${device_id} 的车辆模型`);
      return;
    }

    // 立即更新电量、消耗和距离（不需要动画）
    if (typeof power === 'number') {
      targetModel.battery = power;
    }

    if (typeof consume === 'number') {
      targetModel.consume = consume;
    }

    if (typeof distance === 'number') {
      targetModel.distance = distance;
    }

    // 如果有位置或旋转数据，添加到流畅动画控制器
    if (position || rotation) {
      // 验证和修正位置数据
      const validatedPosition = this.validateAndFixPosition(
        position || targetModel.currentPosition || { x: 0, y: 0, z: 0 }
      );

      const validatedRotation = this.validateAndFixRotation(
        rotation || targetModel.currentRotation || { x: 0, y: 0, z: 0 }
      );

      const dataPoint = {
        position: validatedPosition,
        rotation: validatedRotation,
        power: power,
        consume: consume,
        distance: distance,
        duration: duration,
        timestamp: start_time || Date.now()
      };

      console.log(`设备 ${device_id} 验证后的数据点:`, {
        original: { position, rotation },
        validated: { position: validatedPosition, rotation: validatedRotation }
      });

      this.smoothAnimationController.addDataPoint(device_id, dataPoint);
    }
  }

  /**
   * 执行单个车辆控制命令（原有方法）
   */
  executeVehicleCommand(command) {
    const { device_id, start_time, position, rotation, duration, power, consume, distance } = command;

    console.log(`执行车辆控制命令: ${device_id}`, command);

    // 查找对应的车辆模型
    if (!this.allModels || !Array.isArray(this.allModels)) {
      console.warn('车辆模型列表未设置，无法执行控制命令');
      return;
    }

    const targetModel = this.allModels.find(model => model.id === device_id);
    if (!targetModel) {
      console.warn(`⚠️ 未找到设备ID为 ${device_id} 的车辆模型`);
      return;
    }

    // 如果有duration且大于0，执行顺滑动画
    if (duration && duration > 0 && (position || rotation)) {
      this.executeVehicleAnimationCommand(targetModel, command);
    } else {
      // 立即更新位置和角度（无动画）
      this.executeVehicleImmediateCommand(targetModel, command);
    }

    // 立即更新电量、消耗和距离（不需要动画）
    if (typeof power === 'number') {
      targetModel.battery = power;
    }

    if (typeof consume === 'number') {
      targetModel.consume = consume;
    }

    if (typeof distance === 'number') {
      targetModel.distance = distance;
    }
  }

  /**
   * 执行车辆动画命令（基于duration的顺滑动画）
   */
  executeVehicleAnimationCommand(targetModel, command) {
    const { device_id, start_time, position, rotation, duration, power, consume, distance } = command;

    console.log(`执行车辆动画命令: ${device_id}, 持续时间: ${duration}ms`);

    // 获取当前位置和角度
    const currentPosition = {
      x: targetModel.currentPosition?.x || 0,
      y: targetModel.currentPosition?.y || 0,
      z: targetModel.currentPosition?.z || 0
    };

    const currentRotation = {
      x: targetModel.currentRotation?.x || 0,
      y: targetModel.currentRotation?.y || 0,
      z: targetModel.currentRotation?.z || 0
    };

    // 目标位置和角度
    const targetPosition = position ? { ...currentPosition, ...position } : currentPosition;
    const targetRotation = rotation ? { ...currentRotation, ...rotation } : currentRotation;

    // 创建动画
    this.createVehicleAnimation(targetModel, {
      startPosition: currentPosition,
      targetPosition: targetPosition,
      startRotation: currentRotation,
      targetRotation: targetRotation,
      duration: duration,
      startPower: targetModel.battery || 0,
      targetPower: typeof power === 'number' ? power : targetModel.battery || 0,
      startConsume: targetModel.consume || 0,
      targetConsume: typeof consume === 'number' ? consume : targetModel.consume || 0,
      startDistance: targetModel.distance || 0,
      targetDistance: typeof distance === 'number' ? distance : targetModel.distance || 0
    });
  }

  /**
   * 执行车辆立即命令（无动画）
   */
  executeVehicleImmediateCommand(targetModel, command) {
    const { position, rotation } = command;

    // 立即更新车辆的目标位置和角度
    if (position) {
      // 确保currentPosition存在
      if (!targetModel.currentPosition) {
        targetModel.currentPosition = {};
      }
      Object.assign(targetModel.currentPosition, position);
    }

    if (rotation) {
      // 确保currentRotation存在
      if (!targetModel.currentRotation) {
        targetModel.currentRotation = {};
      }
      Object.assign(targetModel.currentRotation, rotation);
    }
  }

  /**
   * 创建车辆动画（基于WebSocket duration）
   */
  createVehicleAnimation(targetModel, animationConfig) {
    const {
      startPosition,
      targetPosition,
      startRotation,
      targetRotation,
      duration,
      startPower,
      targetPower,
      startConsume,
      targetConsume,
      startDistance,
      targetDistance
    } = animationConfig;

    // 如果已有动画在运行，先停止
    if (targetModel.currentAnimation) {
      targetModel.currentAnimation.stop();
    }

    const startTime = performance.now();
    let animationId = null;

    console.log(`开始车辆匀速动画: ${targetModel.id}`, {
      from: startPosition,
      to: targetPosition,
      duration: `${duration}ms`,
      type: 'linear'
    });

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用匀速线性插值（确保动画衔接）
      const easedProgress = progress;

      // 插值计算当前位置
      const currentPos = {
        x: this.lerp(startPosition.x, targetPosition.x, easedProgress),
        y: this.lerp(startPosition.y, targetPosition.y, easedProgress),
        z: this.lerp(startPosition.z, targetPosition.z, easedProgress)
      };

      // 插值计算当前角度
      const currentRot = {
        x: this.lerp(startRotation.x, targetRotation.x, easedProgress),
        y: this.lerpAngle(startRotation.y, targetRotation.y, easedProgress),
        z: this.lerp(startRotation.z, targetRotation.z, easedProgress)
      };

      // 插值计算当前电量、消耗和距离
      const currentPower = Math.round(this.lerp(startPower, targetPower, easedProgress));
      const currentConsume = Math.round(this.lerp(startConsume, targetConsume, easedProgress));
      const currentDistance = Math.round(this.lerp(startDistance, targetDistance, easedProgress));

      // 更新模型数据
      targetModel.currentPosition = currentPos;
      targetModel.currentRotation = currentRot;
      targetModel.battery = currentPower;
      targetModel.consume = currentConsume;
      targetModel.distance = currentDistance;

      // 如果有场景管理器，通知更新模型位置
      if (this.sceneManager && this.sceneManager.updateModelPosition) {
        this.sceneManager.updateModelPosition(targetModel.id, currentPos, currentRot);
      }

      // 触发位置更新回调（如果存在）
      if (this.onPositionUpdate) {
        this.onPositionUpdate({
          modelId: targetModel.id,
          device_id: targetModel.id,
          position: currentPos,
          rotation: currentRot,
          battery: currentPower,
          consume: currentConsume,
          distance: currentDistance,
          progress: progress
        });
      }

      // 继续动画或完成
      if (progress < 1) {
        animationId = requestAnimationFrame(animate);
      } else {
        // 动画完成
        console.log(`车辆动画完成: ${targetModel.id}`);
        targetModel.currentAnimation = null;

        // 确保最终值精确
        targetModel.currentPosition = targetPosition;
        targetModel.currentRotation = targetRotation;
        targetModel.battery = targetPower;
        targetModel.consume = targetConsume;
        targetModel.distance = targetDistance;
      }
    };

    // 开始动画
    animationId = requestAnimationFrame(animate);

    // 保存动画控制对象
    targetModel.currentAnimation = {
      stop: () => {
        if (animationId) {
          cancelAnimationFrame(animationId);
          animationId = null;
        }
        targetModel.currentAnimation = null;
        console.log(`车辆动画已停止: ${targetModel.id}`);
      }
    };
  }

  /**
   * 线性插值函数
   */
  lerp(start, end, t) {
    return start + (end - start) * t;
  }

  /**
   * 角度插值函数（处理角度环绕）
   */
  lerpAngle(start, end, t) {
    let diff = end - start;

    // 处理角度环绕（选择最短路径）
    if (diff > 180) {
      diff -= 360;
    } else if (diff < -180) {
      diff += 360;
    }

    return start + diff * t;
  }

  /**
   * 平滑缓动函数（已停用，改用匀速线性插值确保动画衔接）
   */
  easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  }

  /**
   * 线性插值函数（匀速动画，确保每段动画能够完美衔接）
   */
  linear(t) {
    return t;
  }

  /**
   * 处理实时路线点
   */
  handleRealtimePoint(data) {
    try {
      const point = this.validateRouteData([data])[0];
      this.moveToPoint(point, true); // 实时移动，不使用动画

      console.log('实时移动到位置:', point.position);
    } catch (error) {
      console.error('处理实时路线点失败:', error);
    }
  }

  /**
   * 处理批量路线数据
   */
  handleBatchRouteData(data) {
    try {
      if (Array.isArray(data)) {
        this.loadRouteData(data);
        console.log('批量路线数据已加载:', data.length, '个点');
      } else {
        console.warn('批量路线数据格式错误，期望数组');
      }
    } catch (error) {
      console.error('处理批量路线数据失败:', error);
    }
  }

  /**
   * 发送 WebSocket 消息
   */
  sendWebSocketMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      try {
        const json = JSON.stringify(message);
        this.websocket.send(json);
        console.log('发送 WebSocket 消息:', message);
      } catch (error) {
        console.error('发送 WebSocket 消息失败:', error);
      }
    } else {
      console.warn('WebSocket 未连接，无法发送消息');
    }
  }
  
  /**
   * 开始路线模拟
   */
  start() {
    console.log('RouteSimulator.start() called');
    console.log('Model available:', !!this.model);
    console.log('Route data length:', this.routeData.length);

    if (!this.model) {
      console.warn('Cannot start: model not available');
      console.log('Model object:', this.model);
      return false;
    }

    if (this.routeData.length === 0) {
      console.warn('Cannot start: route data not available');
      console.log('Route data:', this.routeData);
      return false;
    }

    this.isRunning = true;
    this.isPaused = false;
    this.startTime = performance.now();

    console.log('Route simulation started with', this.routeData.length, 'waypoints');
    this.playRoute();
    return true;
  }
  
  /**
   * 暂停路线模拟
   */
  pause() {
    this.isPaused = true;
    if (this.currentAnimation) {
      this.currentAnimation.pause();
    }
    console.log('Route simulation paused');
  }
  
  /**
   * 恢复路线模拟
   */
  resume() {
    this.isPaused = false;
    if (this.currentAnimation) {
      this.currentAnimation.resume();
    } else {
      this.playRoute();
    }
    console.log('Route simulation resumed');
  }
  
  /**
   * 停止路线模拟
   */
  stop() {
    this.isRunning = false;
    this.isPaused = false;
    this.currentIndex = 0;
    
    if (this.currentAnimation) {
      this.currentAnimation.stop();
      this.currentAnimation = null;
    }
    
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    
    console.log('Route simulation stopped');
  }
  
  /**
   * 仿真路线
   */
  playRoute() {
    if (!this.isRunning || this.isPaused || this.currentIndex >= this.routeData.length) {
      if (this.currentIndex >= this.routeData.length) {
        this.handleRouteComplete();
      }
      return;
    }
    
    const currentPoint = this.routeData[this.currentIndex];
    const nextPoint = this.routeData[this.currentIndex + 1];
    
    if (nextPoint) {
      this.moveToPoint(nextPoint, false, () => {
        this.currentIndex++;
        this.playRoute(); // 继续下一个点
      });
    } else {
      this.handleRouteComplete();
    }
  }
  
  /**
   * 移动到指定点
   */
  moveToPoint(point, immediate = false, onComplete = null) {
    if (!this.model) return;
    console.log("开始拐弯")
    // 特殊处理：当point.position.x == 47.927308061452，移动障碍物模型
    if (point.position.x === 47.927308061452) {
      this.moveObstacleOnSpecialCondition();
    }

    // 使用地面坐标系的位置数据
    const groundPosition = {
      x: point.position.x+675-100,
      y: -6.2,
      z: point.position.z-496-55.5
    };

    const targetRotation = new THREE.Euler(
      THREE.MathUtils.degToRad(point.rotation.x),
      THREE.MathUtils.degToRad(-(point.rotation.y+90)),
      THREE.MathUtils.degToRad(point.rotation.z)
    );

    if (immediate) {
      // 立即移动（用于实时数据）
      if (this.modelComponent) {
        // 使用ModelComponent的方法，确保正确的坐标转换和配置更新
        this.modelComponent.updateCurrentPosition(groundPosition);

        // 更新角度（转换为度数）
        const rotationDegrees = {
          x: point.rotation.x,
          y: point.rotation.y,
          z: point.rotation.z
        };
        this.modelComponent.updateCurrentRotation(rotationDegrees);
      } else {
        // 备用方法：直接设置模型位置和旋转（可能不准确）
        console.warn('ModelComponent not available, using direct position setting');
        const worldPos = this.convertToWorldPosition(groundPosition);
        this.model.position.copy(worldPos);
        this.model.rotation.copy(targetRotation);
      }

      if (this.onPositionUpdate) {
        this.onPositionUpdate(point);
      }
    } else {
      // 动画移动（用于路线仿真）
      const duration = point.duration / this.options.speed;
      this.animateToPoint(groundPosition, targetRotation, duration, onComplete);
    }
  }

  /**
   * 转换地面坐标到世界坐标（备用方法）
   */
  convertToWorldPosition(groundPos) {
    // 这是一个简化的转换，实际应该使用ModelComponent的方法
    const groundOffset = this.modelComponent?.getGroundOffset?.() || 0;
    return new THREE.Vector3(
      groundPos.x,
      groundPos.y + groundOffset,
      groundPos.z
    );
  }
  
  /**
   * 动画移动到指定点
   */
  animateToPoint(groundPosition, targetRotation, duration, onComplete) {
    if (!this.model) return;

    // 转换旋转为度数
    const targetRotationDegrees = {
      x: THREE.MathUtils.radToDeg(targetRotation.x),
      y: THREE.MathUtils.radToDeg(targetRotation.y),
      z: THREE.MathUtils.radToDeg(targetRotation.z)
    };

    // 使用ModelComponent的moveToPositionAndRotation方法进行平滑移动和旋转
    if (this.modelComponent && this.modelComponent.moveToPositionAndRotation) {
      console.log('Using ModelComponent.moveToPositionAndRotation for smooth animation');

      // 计算移动速度（单位/秒）
      const currentPos = this.modelComponent.getCurrentPosition();
      const distance = Math.sqrt(
        Math.pow(groundPosition.x - currentPos.x, 2) +
        Math.pow(groundPosition.y - currentPos.y, 2) +
        Math.pow(groundPosition.z - currentPos.z, 2)
      );
      const speed = distance / (duration / 1000); // 转换为单位/秒

      // 根据插值模式选择缓动类型
      const easingType = this.options.interpolationMode === 'smooth' ? 'easeInOutQuad' : 'linear';

      // 使用ModelComponent的同时移动和旋转方法
      this.modelComponent.moveToPositionAndRotation(groundPosition, targetRotationDegrees, speed, easingType)
        .then(() => {
          if (onComplete) onComplete();
        })
        .catch((error) => {
          console.error('Animation failed:', error);
          if (onComplete) onComplete();
        });
    } else if (this.modelComponent && this.modelComponent.moveToPosition) {
      console.log('Using ModelComponent.moveToPosition for animation (fallback)');

      // 计算移动速度（单位/秒）
      const currentPos = this.modelComponent.getCurrentPosition();
      const distance = Math.sqrt(
        Math.pow(groundPosition.x - currentPos.x, 2) +
        Math.pow(groundPosition.y - currentPos.y, 2) +
        Math.pow(groundPosition.z - currentPos.z, 2)
      );
      const speed = distance / (duration / 1000); // 转换为单位/秒

      // 使用ModelComponent的移动方法
      this.modelComponent.moveToPosition(groundPosition, speed, 'linear', false)
        .then(() => {
          // 平滑设置旋转（如果启用了平滑旋转）
          if (this.options.smoothRotation) {
            this.animateRotationOnly(targetRotation, duration, onComplete); // 500ms旋转动画
          } else {
            this.model.rotation.copy(targetRotation);
            if (onComplete) onComplete();
          }
        })
        .catch((error) => {
          console.error('Animation failed:', error);
          if (onComplete) onComplete();
        });
    } else {
      // 备用方法：直接动画
      console.warn('ModelComponent not available, using direct animation');
      this.animateDirectly(groundPosition, targetRotation, duration, onComplete);
    }
  }

  /**
   * 直接动画移动（备用方法）
   */
  animateDirectly(groundPosition, targetRotation, duration, onComplete) {
    const worldTargetPosition = this.convertToWorldPosition(groundPosition);
    const startPosition = this.model.position.clone();
    const startRotation = this.model.rotation.clone();
    const startTime = performance.now();

    // 计算最短旋转路径（处理角度跨越边界的情况）
    const optimizedTargetRotation = this.optimizeRotationPath(startRotation, targetRotation);

    const animate = (currentTime) => {
      if (!this.isRunning || this.isPaused) return;

      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 选择缓动函数 - 为旋转使用更平滑的缓动
      const positionEasing = this.options.interpolationMode === 'smooth'
        ? EasingFunctions.easeInOutCubic(progress)
        : progress;

      // 旋转使用专门的平滑缓动
      const rotationEasing = this.options.smoothRotation
        ? EasingFunctions.easeInOutQuad(progress)  // 更平滑的旋转缓动
        : progress;

      // 位置插值
      this.model.position.lerpVectors(startPosition, worldTargetPosition, positionEasing);

      // 平滑旋转插值
      if (this.options.smoothRotation) {
        // 使用优化后的旋转路径进行插值
        this.model.rotation.x = this.smoothLerp(startRotation.x, optimizedTargetRotation.x, rotationEasing);
        this.model.rotation.y = this.smoothLerp(startRotation.y, optimizedTargetRotation.y, rotationEasing);
        this.model.rotation.z = this.smoothLerp(startRotation.z, optimizedTargetRotation.z, rotationEasing);
      } else {
        this.model.rotation.copy(targetRotation);
      }

      // 触发位置更新回调
      if (this.onPositionUpdate) {
        this.onPositionUpdate({
          position: this.model.position,
          rotation: this.model.rotation,
          progress: progress
        });
      }

      if (progress < 1) {
        this.animationId = requestAnimationFrame(animate);
      } else {
        if (onComplete) onComplete();
      }
    };

    this.animationId = requestAnimationFrame(animate);
  }

  /**
   * 优化旋转路径，选择最短的旋转方向
   */
  optimizeRotationPath(startRotation, targetRotation) {
    const optimized = targetRotation.clone();

    // 对每个轴进行最短路径优化
    ['x', 'y', 'z'].forEach(axis => {
      const start = startRotation[axis];
      const target = targetRotation[axis];
      const diff = target - start;

      // 如果角度差超过π，选择反方向旋转
      if (Math.abs(diff) > Math.PI) {
        if (diff > 0) {
          optimized[axis] = target - 2 * Math.PI;
        } else {
          optimized[axis] = target + 2 * Math.PI;
        }
      }
    });

    return optimized;
  }

  /**
   * 平滑插值函数，带有额外的平滑处理
   */
  smoothLerp(start, end, t) {
    // 使用更平滑的插值算法
    const smoothT = t * t * (3 - 2 * t); // Smoothstep函数
    return start + (end - start) * smoothT;
  }

  /**
   * 仅旋转动画（用于位置移动完成后的旋转）
   */
  animateRotationOnly(targetRotation, duration, onComplete) {
    if (!this.model) return;

    const startRotation = this.model.rotation.clone();
    const optimizedTargetRotation = this.optimizeRotationPath(startRotation, targetRotation);
    const startTime = performance.now();

    const animate = (currentTime) => {
      if (!this.isRunning || this.isPaused) return;

      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用平滑的旋转缓动
      const rotationEasing = EasingFunctions.easeInOutQuad(progress);

      // 平滑旋转插值
      this.model.rotation.x = this.smoothLerp(startRotation.x, optimizedTargetRotation.x, rotationEasing);
      this.model.rotation.y = this.smoothLerp(startRotation.y, optimizedTargetRotation.y, rotationEasing);
      this.model.rotation.z = this.smoothLerp(startRotation.z, optimizedTargetRotation.z, rotationEasing);

      // 触发位置更新回调
      if (this.onPositionUpdate) {
        this.onPositionUpdate({
          position: this.model.position,
          rotation: this.model.rotation,
          progress: progress
        });
      }

      if (progress < 1) {
        this.animationId = requestAnimationFrame(animate);
      } else {
        if (onComplete) onComplete();
      }
    };

    this.animationId = requestAnimationFrame(animate);
  }
  
  /**
   * 处理路线完成
   */
  handleRouteComplete() {
    console.log('Route simulation completed');
    
    if (this.options.loop && this.routeData.length > 0) {
      this.currentIndex = 0;
      this.playRoute();
    } else {
      this.isRunning = false;
      if (this.onRouteComplete) {
        this.onRouteComplete();
      }
    }
  }
  
  /**
   * 跳转到指定索引
   */
  jumpToIndex(index) {
    if (index >= 0 && index < this.routeData.length) {
      this.currentIndex = index;
      const point = this.routeData[index];
      this.moveToPoint(point, true);
    }
  }
  
  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      currentIndex: this.currentIndex,
      totalPoints: this.routeData.length,
      progress: this.routeData.length > 0 ? this.currentIndex / this.routeData.length : 0
    };
  }

  /**
   * 设置旋转平滑选项
   */
  setSmoothRotation(enabled) {
    this.options.smoothRotation = enabled;
    console.log('Smooth rotation:', enabled ? 'enabled' : 'disabled');
  }

  /**
   * 设置插值模式
   */
  setInterpolationMode(mode) {
    this.options.interpolationMode = mode;
    console.log('Interpolation mode set to:', mode);
  }
  
  /**
   * 断开WebSocket连接
   */
  disconnect() {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
  }
  
  /**
   * 启用/禁用流畅动画
   */
  setSmoothAnimation(enabled) {
    this.options.enableSmoothAnimation = enabled;

    if (!enabled) {
      // 如果禁用流畅动画，停止所有流畅动画
      this.smoothAnimationController.stopAllAnimations();
    }

    console.log(`流畅动画已${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 获取流畅动画状态
   */
  getSmoothAnimationStatus() {
    return {
      enabled: this.options.enableSmoothAnimation,
      performanceStats: this.smoothAnimationController.getPerformanceStats(),
      deviceStatuses: this.allModels ? this.allModels.map(model => ({
        deviceId: model.id,
        status: this.smoothAnimationController.getDeviceStatus(model.id)
      })) : []
    };
  }

  /**
   * 清理指定设备的流畅动画数据
   */
  clearSmoothAnimationData(deviceId) {
    this.smoothAnimationController.clearDevice(deviceId);
    console.log(`清理设备 ${deviceId} 的流畅动画数据`);
  }

  /**
   * 清理所有流畅动画数据
   */
  clearAllSmoothAnimationData() {
    this.smoothAnimationController.clearAll();
    console.log('清理所有流畅动画数据');
  }

  /**
   * 验证和修正位置数据
   */
  validateAndFixPosition(position) {
    const validated = {
      x: this.validateNumber(position.x, 0),
      y: this.validateNumber(position.y, 0), // 直接使用原始Y值，不进行任何修正
      z: this.validateNumber(position.z, 0)
    };

    return validated;
  }

  /**
   * 验证和修正旋转数据
   */
  validateAndFixRotation(rotation) {
    return {
      x: this.normalizeAngle(this.validateNumber(rotation.x, 0)),
      y: this.normalizeAngle(this.validateNumber(rotation.y, 0)),
      z: this.normalizeAngle(this.validateNumber(rotation.z, 0))
    };
  }

  /**
   * 验证数字有效性
   */
  validateNumber(value, defaultValue = 0) {
    if (typeof value !== 'number' || isNaN(value) || !isFinite(value)) {
      console.warn(`⚠️ 无效数值 (${value})，使用默认值 ${defaultValue}`);
      return defaultValue;
    }
    return value;
  }

  /**
   * 标准化角度到 -180 到 180 度范围
   */
  normalizeAngle(angle) {
    while (angle > 180) angle -= 360;
    while (angle < -180) angle += 360;
    return angle;
  }

  /**
   * 测试空数组处理
   */
  testEmptyArrayHandling() {
    console.log('测试空数组处理...');

    // 先发送一些数据
    const testData = [
      {
        device_id: 'test_car',
        position: { x: 100, y: 0, z: 200 },
        rotation: { x: 0, y: 45, z: 0 },
        power: 80,
        duration: 1000
      }
    ];

    console.log('发送测试数据:', testData);
    this.handleVehicleControlMessage(testData);

    // 2秒后发送空数组
    setTimeout(() => {
      console.log('发送空数组');
      this.handleVehicleControlMessage([]);

      // 检查动画状态
      if (this.smoothAnimationController) {
        const status = this.getSmoothAnimationStatus();
        console.log('空数组处理后的状态:', status);
      }
    }, 2000);
  }

  /**
   * 测试Y坐标修正
   */
  testYCoordinateFix() {
    console.log('测试Y坐标修正...');

    const testData = [
      {
        device_id: 'test_car_y',
        position: { x: 100, y: -50, z: 200 }, // 异常的Y坐标
        rotation: { x: 0, y: 45, z: 0 },
        power: 80,
        duration: 1000
      }
    ];

    console.log('发送异常Y坐标数据:', testData);
    this.handleVehicleControlMessage(testData);
  }

  /**
   * 测试Y坐标显示问题
   */
  testYCoordinateShaking() {
    console.log('测试Y坐标显示问题修复...');

    // 发送一系列Y坐标都为0的数据，验证模型显示在正确的高度
    const testSequence = [
      { x: 100, y: 0, z: 200 },
      { x: 110, y: 0, z: 210 },
      { x: 120, y: 0, z: 220 },
      { x: 130, y: 0, z: 230 },
      { x: 140, y: 0, z: 240 }
    ];

    console.log('测试说明: Y坐标为0，模型应该显示在地面上，不会跳动或显示在地面以下');

    testSequence.forEach((position, index) => {
      setTimeout(() => {
        const testData = [{
          device_id: 'test_y_display_car',
          position: position,
          rotation: { x: 0, y: 45, z: 0 },
          power: 80 - index * 5,
          duration: 1000
        }];

        console.log(`发送测试数据 ${index + 1}: Y坐标=${testData[0].position.y} (模型应该显示在地面上)`);
        this.handleVehicleControlMessage(testData);
      }, index * 1000);
    });

    console.log('Y坐标显示测试启动，观察模型是否正确显示在地面上');
  }

  /**
   * 测试不同Y坐标值的显示
   */
  testDifferentYCoordinates() {
    console.log('测试不同Y坐标值的显示...');

    const testSequence = [
      { x: 100, y: 0, z: 200, desc: '地面' },
      { x: 110, y: 5, z: 210, desc: '5米高' },
      { x: 120, y: 10, z: 220, desc: '10米高' },
      { x: 130, y: -5, z: 230, desc: '地下5米' },
      { x: 140, y: 0, z: 240, desc: '回到地面' }
    ];

    testSequence.forEach((data, index) => {
      setTimeout(() => {
        const testData = [{
          device_id: 'test_y_values_car',
          position: { x: data.x, y: data.y, z: data.z },
          rotation: { x: 0, y: 45, z: 0 },
          power: 80 - index * 5,
          duration: 1000
        }];

        console.log(`发送测试数据 ${index + 1}: Y=${data.y} (${data.desc})`);
        this.handleVehicleControlMessage(testData);
      }, index * 1500);
    });

    console.log('不同Y坐标测试启动，观察模型高度变化是否正确');
  }

  /**
   * 测试延时队列效果
   */
  testUniformMotion() {
    console.log('测试延时队列动画效果...');

    // 确保启用延时队列模式
    if (this.smoothAnimationController) {
      this.smoothAnimationController.options.uniformMotion = true;
      console.log('已启用延时队列模式');
      console.log(`延时时间: ${this.smoothAnimationController.options.delayTime}ms`);
      console.log(`处理间隔: ${this.smoothAnimationController.options.processInterval}ms`);
    }

    // 发送一系列数据点，模拟车辆沿直线行驶
    const testSequence = [
      { x: 0, y: 0, z: 0 },
      { x: 20, y: 0, z: 0 },
      { x: 40, y: 0, z: 0 },
      { x: 60, y: 0, z: 0 },
      { x: 80, y: 0, z: 0 },
      { x: 100, y: 0, z: 0 }
    ];

    console.log('测试说明:');
    console.log('  1. 数据立即入队');
    console.log('  2. 第一个数据延时2秒后开始处理');
    console.log('  3. 后续数据每秒处理一个，实现流畅衔接');

    testSequence.forEach((position, index) => {
      setTimeout(() => {
        const testData = [{
          device_id: 'test_queue_car',
          position: position,
          rotation: { x: 0, y: 90, z: 0 }, // 朝向X轴正方向
          power: 80 - index * 2,
          duration: 1000
        }];

        console.log(`[${new Date().toLocaleTimeString()}] 发送数据 ${index + 1}: 位置=(${position.x}, ${position.y}, ${position.z})`);
        this.handleVehicleControlMessage(testData);

        // 显示队列状态
        if (this.smoothAnimationController) {
          const status = this.smoothAnimationController.getDeviceStatus('test_queue_car');
          console.log(`队列状态: 长度=${status.queueSize}, 处理中=${status.isProcessing}`);
        }
      }, index * 500); // 每500ms发送一个数据点（比处理速度快）
    });

    console.log('延时队列测试启动，观察数据入队和处理过程');
  }

  /**
   * 测试数据流调试
   */
  testDataFlow() {
    console.log('测试数据流调试...');

    // 检查关键对象是否存在
    console.log('检查关键对象:');
    console.log('  - smoothAnimationController:', !!this.smoothAnimationController);
    console.log('  - sceneManager:', !!this.sceneManager);
    console.log('  - allModels:', !!this.allModels, '长度:', this.allModels ? this.allModels.length : 'N/A');

    // 发送一个简单的测试数据
    const testData = [{
      device_id: 'test_debug_car',
      position: { x: 50, y: 0, z: 50 },
      rotation: { x: 0, y: 90, z: 0 },
      power: 80,
      duration: 2000 // 2秒动画
    }];

    console.log('发送调试数据:', testData[0]);
    console.log('观察控制台日志，追踪数据流向:');
    console.log('  1. 数据入队');
    console.log('  2. 延时2秒后开始处理');
    console.log('  3. 动画更新');
    console.log('  4. 3D模型更新');

    this.handleVehicleControlMessage(testData);

    // 显示队列状态
    setTimeout(() => {
      if (this.smoothAnimationController) {
        const status = this.smoothAnimationController.getDeviceStatus('test_debug_car');
        console.log('1秒后队列状态:', status);
      }
    }, 1000);

    // 5秒后发送第二个数据点
    setTimeout(() => {
      const testData2 = [{
        device_id: 'test_debug_car',
        position: { x: 100, y: 0, z: 100 },
        rotation: { x: 0, y: 90, z: 0 },
        power: 75,
        duration: 1500 // 1.5秒动画
      }];

      console.log('发送第二个调试数据:', testData2[0]);
      this.handleVehicleControlMessage(testData2);
    }, 5000);
  }

  /**
   * 测试基于duration的匀速动画
   */
  testQueueBuffer() {
    console.log('测试基于duration的匀速动画...');

    // 发送带有不同duration的数据点
    const testSequence = [
      { x: 0, y: 0, z: 0, duration: 1000 },     // 1秒到达
      { x: 30, y: 0, z: 0, duration: 1500 },    // 1.5秒到达
      { x: 60, y: 0, z: 0, duration: 1000 },    // 1秒到达
      { x: 90, y: 0, z: 0, duration: 2000 },    // 2秒到达
      { x: 120, y: 0, z: 0, duration: 1000 },   // 1秒到达
      { x: 150, y: 0, z: 0, duration: 1500 },   // 1.5秒到达
      { x: 180, y: 0, z: 0, duration: 1000 }    // 1秒到达
    ];

    console.log('测试说明: 验证基于duration的匀速动画');
    console.log('  - 每个数据点都有指定的duration');
    console.log('  - 延时2秒后开始处理');
    console.log('  - 根据duration进行精确的匀速插值');
    console.log('  - 一段动画完成后立即开始下一段');
    console.log('  - 实现真正的无缝衔接');

    testSequence.forEach((data, index) => {
      setTimeout(() => {
        const testData = [{
          device_id: 'test_duration_car',
          position: { x: data.x, y: data.y, z: data.z },
          rotation: { x: 0, y: 90, z: 0 },
          power: 90 - index * 3,
          duration: data.duration
        }];

        console.log(`[${new Date().toLocaleTimeString()}] 发送duration动画数据 ${index + 1}: 目标=(${data.x}, ${data.y}, ${data.z}), 时长=${data.duration}ms`);
        this.handleVehicleControlMessage(testData);

        // 显示动画状态
        if (this.smoothAnimationController) {
          setTimeout(() => {
            const status = this.smoothAnimationController.getDeviceStatus('test_duration_car');
            console.log(`动画状态: 队列=${status.queueSize}, 动画中=${status.isAnimating}`);
          }, 100);
        }
      }, index * 300); // 每300ms发送一个数据点（比动画时间短，测试队列缓冲）
    });

    console.log('Duration动画测试启动，观察车辆是否按duration精确移动');
  }

  /**
   * 直接更新3D模型位置（跳过ModelComponent动画）
   */
  directUpdateModel3D(deviceId, position, rotation) {
    if (!this.sceneManager || !this.sceneManager.getModelRef) {
      console.warn(`⚠️ sceneManager 或 getModelRef 未初始化`);
      return;
    }

    // 获取模型引用
    const modelRef = this.sceneManager.getModelRef(deviceId);
    if (!modelRef || !modelRef.getModel) {
      console.warn(`⚠️ 未找到设备 ${deviceId} 的模型引用`);
      return;
    }

    // 获取Three.js模型对象
    const model3D = modelRef.getModel();
    if (!model3D) {
      console.warn(`⚠️ 设备 ${deviceId} 的3D模型对象为空`);
      return;
    }

    try {
      // 调试日志
      if (Math.random() < 0.01) { // 1%的概率输出日志
        console.log(`直接更新3D模型: ${deviceId}, 位置=(${position.x.toFixed(1)}, ${position.z.toFixed(1)})`);
      }

      // 直接设置3D模型的位置
      if (position) {
        model3D.position.set(
          position.x !== undefined ? position.x : model3D.position.x,
          position.y !== undefined ? position.y : model3D.position.y,
          position.z !== undefined ? position.z : model3D.position.z
        );
      }

      // 直接设置3D模型的旋转
      if (rotation) {
        model3D.rotation.set(
          rotation.x !== undefined ? (rotation.x * Math.PI / 180) : model3D.rotation.x,
          rotation.y !== undefined ? (rotation.y * Math.PI / 180) : model3D.rotation.y,
          rotation.z !== undefined ? (rotation.z * Math.PI / 180) : model3D.rotation.z
        );
      }

      // 更新ModelComponent的内部状态，保持同步
      if (modelRef.updateCurrentPosition && position) {
        modelRef.currentPosition = { ...position };
      }
      if (modelRef.updateCurrentRotation && rotation) {
        modelRef.currentRotation = { ...rotation };
      }

    } catch (error) {
      console.error(`❌ 直接更新模型 ${deviceId} 失败:`, error);
    }
  }

  /**
   * 销毁路线模拟器
   */
  destroy() {
    this.stop();
    this.disconnect();

    // 清理流畅动画控制器
    if (this.smoothAnimationController) {
      this.smoothAnimationController.clearAll();
    }

    this.model = null;
    this.routeData = [];
    console.log('RouteSimulator destroyed');
  }

  /**
   * 特殊条件下移动障碍物模型
   */
  moveObstacleOnSpecialCondition() {
    console.log('触发特殊条件：移动障碍物模型');

    // 获取场景中的所有障碍物
    if (window.sceneManagerRef && window.sceneManagerRef.getScene) {
      const scene = window.sceneManagerRef.getScene();

      // 查找障碍物模型
      const obstacles = scene.children.filter(child =>
        child.userData && child.userData.type === 'obstacle'
      );

      console.log(`找到 ${obstacles.length} 个障碍物模型`);

      obstacles.forEach((obstacle, index) => {
        console.log(`移动障碍物 ${index + 1}: ${obstacle.userData.name || 'Unknown'}`);

        // 起始位置
        const startPosition = { x: 680, y: 0, z: -430 };
        // 目标位置
        const endPosition = { x: 680, y: 0, z: -500 };

        // 设置起始位置
        obstacle.position.set(startPosition.x, startPosition.y, startPosition.z);

        // 创建动画移动到目标位置
        this.animateObstacle(obstacle, startPosition, endPosition, 20000); // 2秒动画
      });
    } else {
      console.warn('⚠️ 场景管理器未找到，无法移动障碍物');
    }
  }

  /**
   * 动画移动障碍物
   */
  animateObstacle(obstacle, startPos, endPos, duration) {
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 线性插值计算当前位置
      const currentPos = {
        x: startPos.x + (endPos.x - startPos.x) * progress,
        y: startPos.y + (endPos.y - startPos.y) * progress,
        z: startPos.z + (endPos.z - startPos.z) * progress
      };

      // 更新障碍物位置
      obstacle.position.set(currentPos.x, currentPos.y, currentPos.z);

      // 如果动画未完成，继续下一帧
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        console.log(`障碍物移动完成: (${endPos.x}, ${endPos.y}, ${endPos.z})`);
      }
    };

    console.log(`开始障碍物动画: (${startPos.x}, ${startPos.y}, ${startPos.z}) -> (${endPos.x}, ${endPos.y}, ${endPos.z})`);
    animate();
  }
}

export default RouteSimulator;
